package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 小丑牌注册表
 */
public class JokerRegistry {
    private final Map<String, Joker> jokerTemplates;
    private final Map<String, JokerEffect> jokerEffects;
    private final Random random;

    public JokerRegistry() {
        this.jokerTemplates = new HashMap<>();
        this.jokerEffects = new HashMap<>();
        this.random = new Random();
        initializeJokers();
    }

    /**
     * 初始化所有小丑牌
     */
    private void initializeJokers() {
        // 基础小丑牌
        registerJoker("joker", "小丑", "+4倍数", JokerRarity.COMMON, 2, new BasicMultJokerEffect(4));

        // 花色小丑牌
        registerJoker("greedy_joker", "贪婪小丑", "方块牌计分时+3倍数", JokerRarity.COMMON, 5, new SuitMultJokerEffect(Suit.DIAMONDS, 3));
        registerJoker("lusty_joker", "好色小丑", "红桃牌计分时+3倍数", JokerRarity.COMMON, 5, new SuitMultJokerEffect(Suit.HEARTS, 3));
        registerJoker("wrathful_joker", "愤怒小丑", "黑桃牌计分时+3倍数", JokerRarity.COMMON, 5, new SuitMultJokerEffect(Suit.SPADES, 3));
        registerJoker("gluttonous_joker", "贪食小丑", "梅花牌计分时+3倍数", JokerRarity.COMMON, 5, new SuitMultJokerEffect(Suit.CLUBS, 3));

        // 手牌类型小丑牌
        registerJoker("jolly_joker", "快乐小丑", "对子+8倍数", JokerRarity.COMMON, 3, new HandTypeMultJokerEffect(PokerHand.PAIR, 8));
        registerJoker("zany_joker", "滑稽小丑", "三条+12倍数", JokerRarity.COMMON, 4, new HandTypeMultJokerEffect(PokerHand.THREE_OF_A_KIND, 12));
        registerJoker("mad_joker", "疯狂小丑", "两对+10倍数", JokerRarity.COMMON, 4, new HandTypeMultJokerEffect(PokerHand.TWO_PAIR, 10));
        registerJoker("crazy_joker", "疯癫小丑", "顺子+12倍数", JokerRarity.COMMON, 4, new HandTypeMultJokerEffect(PokerHand.STRAIGHT, 12));
        registerJoker("droll_joker", "古怪小丑", "同花+10倍数", JokerRarity.COMMON, 4, new HandTypeMultJokerEffect(PokerHand.FLUSH, 10));

        // 筹码小丑牌
        registerJoker("sly_joker", "狡猾小丑", "对子+50筹码", JokerRarity.COMMON, 3, new HandTypeChipsJokerEffect(PokerHand.PAIR, 50));
        registerJoker("wily_joker", "机智小丑", "三条+100筹码", JokerRarity.COMMON, 4, new HandTypeChipsJokerEffect(PokerHand.THREE_OF_A_KIND, 100));
        registerJoker("clever_joker", "聪明小丑", "两对+80筹码", JokerRarity.COMMON, 4, new HandTypeChipsJokerEffect(PokerHand.TWO_PAIR, 80));
        registerJoker("devious_joker", "狡诈小丑", "顺子+100筹码", JokerRarity.COMMON, 4, new HandTypeChipsJokerEffect(PokerHand.STRAIGHT, 100));
        registerJoker("crafty_joker", "狡猾小丑", "同花+80筹码", JokerRarity.COMMON, 4, new HandTypeChipsJokerEffect(PokerHand.FLUSH, 80));

        // 特殊小丑牌
        registerJoker("half_joker", "半个小丑", "3张牌或更少时+20倍数", JokerRarity.COMMON, 5, new SmallHandJokerEffect());
        registerJoker("banner", "横幅", "每剩余弃牌+30筹码", JokerRarity.COMMON, 5, new BannerJokerEffect());

        // 更多小丑牌...
        // 这里只实现了一部分，完整版本需要实现所有150个小丑牌
    }

    /**
     * 注册小丑牌
     */
    private void registerJoker(String id, String name, String description, JokerRarity rarity, int cost, JokerEffect effect) {
        Joker joker = new Joker(id, name, description, rarity, cost);
        jokerTemplates.put(id, joker);
        jokerEffects.put(id, effect);
    }

    /**
     * 创建小丑牌实例
     */
    public Joker createJoker(String id) {
        Joker template = jokerTemplates.get(id);
        if (template == null) return null;

        // 创建副本
        Joker joker = new Joker(template.getId(), template.getName(), template.getDescription(),
                               template.getRarity(), template.getBaseCost());

        // 复制模板的初始状态
        joker.setChips(template.getChips());
        joker.setMult(template.getMult());
        joker.setXMult(template.getXMult());
        joker.setMoney(template.getMoney());

        return joker;
    }

    /**
     * 获取小丑牌效果
     */
    public JokerEffect getEffect(String id) {
        return jokerEffects.get(id);
    }

    /**
     * 获取所有小丑牌ID
     */
    public Set<String> getAllJokerIds() {
        return jokerTemplates.keySet();
    }

    /**
     * 根据稀有度获取随机小丑牌
     */
    public Joker getRandomJoker(JokerRarity rarity) {
        List<String> jokersOfRarity = jokerTemplates.values().stream()
            .filter(j -> j.getRarity() == rarity)
            .map(Joker::getId)
            .collect(Collectors.toList());

        if (jokersOfRarity.isEmpty()) return null;

        String randomId = jokersOfRarity.get(random.nextInt(jokersOfRarity.size()));
        return createJoker(randomId);
    }

    /**
     * 获取随机小丑牌（按概率）
     */
    public Joker getRandomJoker() {
        double rand = random.nextDouble();

        if (rand < JokerRarity.COMMON.getProbability()) {
            return getRandomJoker(JokerRarity.COMMON);
        } else if (rand < JokerRarity.COMMON.getProbability() + JokerRarity.UNCOMMON.getProbability()) {
            return getRandomJoker(JokerRarity.UNCOMMON);
        } else {
            return getRandomJoker(JokerRarity.RARE);
        }
    }

    // 小丑牌效果实现类

    /**
     * 基础倍数小丑牌效果
     */
    private static class BasicMultJokerEffect implements JokerEffect {
        private final int mult;

        public BasicMultJokerEffect(int mult) {
            this.mult = mult;
        }

        @Override
        public void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {
            context.addMult(mult);
        }
    }

    /**
     * 花色倍数小丑牌效果
     */
    private static class SuitMultJokerEffect implements JokerEffect {
        private final Suit suit;
        private final int mult;

        public SuitMultJokerEffect(Suit suit, int mult) {
            this.suit = suit;
            this.mult = mult;
        }

        @Override
        public void onCardScored(Joker joker, Card card, ScoreContext context, GameState gameState) {
            if (card.getSuit() == suit) {
                context.addMult(mult);
            }
        }
    }

    /**
     * 手牌类型倍数小丑牌效果
     */
    private static class HandTypeMultJokerEffect implements JokerEffect {
        private final PokerHand handType;
        private final int mult;

        public HandTypeMultJokerEffect(PokerHand handType, int mult) {
            this.handType = handType;
            this.mult = mult;
        }

        @Override
        public void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {
            if (containsHandType(handResult.getHandType(), handType)) {
                context.addMult(mult);
            }
        }

        private boolean containsHandType(PokerHand actual, PokerHand required) {
            // 简化的包含关系检查
            if (actual == required) return true;

            // 更高级的手牌包含低级手牌
            switch (required) {
                case PAIR:
                    return actual == PokerHand.PAIR || actual == PokerHand.TWO_PAIR ||
                           actual == PokerHand.THREE_OF_A_KIND || actual == PokerHand.FULL_HOUSE ||
                           actual == PokerHand.FOUR_OF_A_KIND || actual == PokerHand.FIVE_OF_A_KIND;
                case FLUSH:
                    return actual == PokerHand.FLUSH || actual == PokerHand.STRAIGHT_FLUSH ||
                           actual == PokerHand.ROYAL_FLUSH || actual == PokerHand.FLUSH_HOUSE ||
                           actual == PokerHand.FLUSH_FIVE;
                case STRAIGHT:
                    return actual == PokerHand.STRAIGHT || actual == PokerHand.STRAIGHT_FLUSH ||
                           actual == PokerHand.ROYAL_FLUSH;
                default:
                    return false;
            }
        }
    }

    /**
     * 手牌类型筹码小丑牌效果
     */
    private static class HandTypeChipsJokerEffect implements JokerEffect {
        private final PokerHand handType;
        private final int chips;

        public HandTypeChipsJokerEffect(PokerHand handType, int chips) {
            this.handType = handType;
            this.chips = chips;
        }

        @Override
        public void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {
            if (containsHandType(handResult.getHandType(), handType)) {
                context.addChips(chips);
            }
        }

        private boolean containsHandType(PokerHand actual, PokerHand required) {
            // 与上面相同的逻辑
            if (actual == required) return true;

            switch (required) {
                case PAIR:
                    return actual == PokerHand.PAIR || actual == PokerHand.TWO_PAIR ||
                           actual == PokerHand.THREE_OF_A_KIND || actual == PokerHand.FULL_HOUSE ||
                           actual == PokerHand.FOUR_OF_A_KIND || actual == PokerHand.FIVE_OF_A_KIND;
                case FLUSH:
                    return actual == PokerHand.FLUSH || actual == PokerHand.STRAIGHT_FLUSH ||
                           actual == PokerHand.ROYAL_FLUSH || actual == PokerHand.FLUSH_HOUSE ||
                           actual == PokerHand.FLUSH_FIVE;
                case STRAIGHT:
                    return actual == PokerHand.STRAIGHT || actual == PokerHand.STRAIGHT_FLUSH ||
                           actual == PokerHand.ROYAL_FLUSH;
                default:
                    return false;
            }
        }
    }

    /**
     * 小手牌小丑牌效果
     */
    private static class SmallHandJokerEffect implements JokerEffect {
        @Override
        public void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {
            if (handResult.getAllCards().size() <= 3) {
                context.addMult(20);
            }
        }
    }

    /**
     * 横幅小丑牌效果
     */
    private static class BannerJokerEffect implements JokerEffect {
        @Override
        public void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {
            context.addChips(gameState.getDiscards() * 30);
        }
    }
}
