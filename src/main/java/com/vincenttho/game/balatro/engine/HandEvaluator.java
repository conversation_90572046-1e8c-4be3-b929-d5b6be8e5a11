package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 手牌识别引擎
 */
public class HandEvaluator {
    
    /**
     * 识别手牌类型
     */
    public static HandResult evaluateHand(List<Card> cards) {
        if (cards.isEmpty()) {
            return new HandResult(PokerHand.HIGH_CARD, new ArrayList<>(), cards);
        }
        
        // 按优先级从高到低检查手牌类型
        HandResult result;
        
        // 检查秘密手牌
        result = checkFlushFive(cards);
        if (result != null) return result;
        
        result = checkFlushHouse(cards);
        if (result != null) return result;
        
        result = checkFiveOfAKind(cards);
        if (result != null) return result;
        
        // 检查常规手牌
        result = checkRoyalFlush(cards);
        if (result != null) return result;
        
        result = checkStraightFlush(cards);
        if (result != null) return result;
        
        result = checkFourOfAKind(cards);
        if (result != null) return result;
        
        result = checkFullHouse(cards);
        if (result != null) return result;
        
        result = checkFlush(cards);
        if (result != null) return result;
        
        result = checkStraight(cards);
        if (result != null) return result;
        
        result = checkThreeOfAKind(cards);
        if (result != null) return result;
        
        result = checkTwoPair(cards);
        if (result != null) return result;
        
        result = checkPair(cards);
        if (result != null) return result;
        
        // 默认为高牌
        return checkHighCard(cards);
    }
    
    private static HandResult checkFlushFive(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        for (Map.Entry<Rank, List<Card>> entry : rankGroups.entrySet()) {
            List<Card> sameRank = entry.getValue();
            if (sameRank.size() >= 5) {
                // 检查是否同花色
                Map<Suit, List<Card>> suitGroups = sameRank.stream()
                    .collect(Collectors.groupingBy(Card::getSuit));
                
                for (List<Card> sameSuit : suitGroups.values()) {
                    if (sameSuit.size() >= 5) {
                        List<Card> scoringCards = sameSuit.subList(0, 5);
                        return new HandResult(PokerHand.FLUSH_FIVE, scoringCards, cards);
                    }
                }
            }
        }
        return null;
    }
    
    private static HandResult checkFlushHouse(List<Card> cards) {
        Map<Suit, List<Card>> suitGroups = groupBySuit(cards);
        
        for (List<Card> sameSuit : suitGroups.values()) {
            if (sameSuit.size() >= 5) {
                // 在同花色中检查葫芦
                Map<Rank, List<Card>> rankGroups = sameSuit.stream()
                    .collect(Collectors.groupingBy(Card::getRank));
                
                List<Card> threeOfAKind = null;
                List<Card> pair = null;
                
                for (List<Card> sameRank : rankGroups.values()) {
                    if (sameRank.size() >= 3 && threeOfAKind == null) {
                        threeOfAKind = sameRank.subList(0, 3);
                    } else if (sameRank.size() >= 2 && pair == null) {
                        pair = sameRank.subList(0, 2);
                    }
                }
                
                if (threeOfAKind != null && pair != null) {
                    List<Card> scoringCards = new ArrayList<>();
                    scoringCards.addAll(threeOfAKind);
                    scoringCards.addAll(pair);
                    return new HandResult(PokerHand.FLUSH_HOUSE, scoringCards, cards);
                }
            }
        }
        return null;
    }
    
    private static HandResult checkFiveOfAKind(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 5) {
                List<Card> scoringCards = sameRank.subList(0, 5);
                return new HandResult(PokerHand.FIVE_OF_A_KIND, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkRoyalFlush(List<Card> cards) {
        HandResult straightFlush = checkStraightFlush(cards);
        if (straightFlush != null) {
            List<Card> scoringCards = straightFlush.getScoringCards();
            // 检查是否为A-K-Q-J-10
            if (scoringCards.stream().anyMatch(c -> c.getRank() == Rank.ACE) &&
                scoringCards.stream().anyMatch(c -> c.getRank() == Rank.KING)) {
                return new HandResult(PokerHand.ROYAL_FLUSH, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkStraightFlush(List<Card> cards) {
        Map<Suit, List<Card>> suitGroups = groupBySuit(cards);
        
        for (List<Card> sameSuit : suitGroups.values()) {
            if (sameSuit.size() >= 5) {
                HandResult straight = checkStraightInCards(sameSuit);
                if (straight != null) {
                    return new HandResult(PokerHand.STRAIGHT_FLUSH, straight.getScoringCards(), cards);
                }
            }
        }
        return null;
    }
    
    private static HandResult checkFourOfAKind(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 4) {
                List<Card> scoringCards = sameRank.subList(0, 4);
                return new HandResult(PokerHand.FOUR_OF_A_KIND, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkFullHouse(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        List<Card> threeOfAKind = null;
        List<Card> pair = null;
        
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 3 && threeOfAKind == null) {
                threeOfAKind = sameRank.subList(0, 3);
            } else if (sameRank.size() >= 2 && pair == null) {
                pair = sameRank.subList(0, 2);
            }
        }
        
        if (threeOfAKind != null && pair != null) {
            List<Card> scoringCards = new ArrayList<>();
            scoringCards.addAll(threeOfAKind);
            scoringCards.addAll(pair);
            return new HandResult(PokerHand.FULL_HOUSE, scoringCards, cards);
        }
        return null;
    }
    
    private static HandResult checkFlush(List<Card> cards) {
        Map<Suit, List<Card>> suitGroups = groupBySuit(cards);
        
        for (List<Card> sameSuit : suitGroups.values()) {
            if (sameSuit.size() >= 5) {
                List<Card> scoringCards = sameSuit.subList(0, 5);
                return new HandResult(PokerHand.FLUSH, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkStraight(List<Card> cards) {
        return checkStraightInCards(cards);
    }
    
    private static HandResult checkStraightInCards(List<Card> cards) {
        Set<Integer> values = cards.stream()
            .map(c -> c.getRank().getValue())
            .collect(Collectors.toSet());
        
        // 检查A-2-3-4-5的特殊情况
        if (values.contains(14) && values.contains(2) && values.contains(3) && 
            values.contains(4) && values.contains(5)) {
            List<Card> scoringCards = cards.stream()
                .filter(c -> c.getRank().getValue() <= 5 || c.getRank() == Rank.ACE)
                .limit(5)
                .collect(Collectors.toList());
            return new HandResult(PokerHand.STRAIGHT, scoringCards, cards);
        }
        
        // 检查常规顺子
        List<Integer> sortedValues = values.stream().sorted().collect(Collectors.toList());
        
        for (int i = 0; i <= sortedValues.size() - 5; i++) {
            boolean isStraight = true;
            for (int j = 1; j < 5; j++) {
                if (sortedValues.get(i + j) != sortedValues.get(i) + j) {
                    isStraight = false;
                    break;
                }
            }
            
            if (isStraight) {
                int startValue = sortedValues.get(i);
                List<Card> scoringCards = cards.stream()
                    .filter(c -> c.getRank().getValue() >= startValue && 
                               c.getRank().getValue() <= startValue + 4)
                    .limit(5)
                    .collect(Collectors.toList());
                return new HandResult(PokerHand.STRAIGHT, scoringCards, cards);
            }
        }
        
        return null;
    }
    
    private static HandResult checkThreeOfAKind(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 3) {
                List<Card> scoringCards = sameRank.subList(0, 3);
                return new HandResult(PokerHand.THREE_OF_A_KIND, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkTwoPair(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        List<List<Card>> pairs = new ArrayList<>();
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 2) {
                pairs.add(sameRank.subList(0, 2));
            }
        }
        
        if (pairs.size() >= 2) {
            List<Card> scoringCards = new ArrayList<>();
            scoringCards.addAll(pairs.get(0));
            scoringCards.addAll(pairs.get(1));
            return new HandResult(PokerHand.TWO_PAIR, scoringCards, cards);
        }
        return null;
    }
    
    private static HandResult checkPair(List<Card> cards) {
        Map<Rank, List<Card>> rankGroups = groupByRank(cards);
        
        for (List<Card> sameRank : rankGroups.values()) {
            if (sameRank.size() >= 2) {
                List<Card> scoringCards = sameRank.subList(0, 2);
                return new HandResult(PokerHand.PAIR, scoringCards, cards);
            }
        }
        return null;
    }
    
    private static HandResult checkHighCard(List<Card> cards) {
        if (cards.isEmpty()) {
            return new HandResult(PokerHand.HIGH_CARD, new ArrayList<>(), cards);
        }
        
        // 找到最高的牌
        Card highestCard = cards.stream()
            .max(Comparator.comparing(c -> c.getRank().getValue()))
            .orElse(cards.get(0));
        
        List<Card> scoringCards = Arrays.asList(highestCard);
        return new HandResult(PokerHand.HIGH_CARD, scoringCards, cards);
    }
    
    private static Map<Rank, List<Card>> groupByRank(List<Card> cards) {
        return cards.stream()
            .collect(Collectors.groupingBy(Card::getRank));
    }
    
    private static Map<Suit, List<Card>> groupBySuit(List<Card> cards) {
        return cards.stream()
            .collect(Collectors.groupingBy(Card::getSuit));
    }
}
