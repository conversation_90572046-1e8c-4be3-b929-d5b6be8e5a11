package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;

/**
 * 商店生成器
 */
public class ShopGenerator {
    private final Random random;
    private final JokerRegistry jokerRegistry;
    
    // 权重配置
    private static final int JOKER_WEIGHT = 20;
    private static final int TAROT_WEIGHT = 4;
    private static final int PLANET_WEIGHT = 4;
    private static final int TOTAL_WEIGHT = JOKER_WEIGHT + TAROT_WEIGHT + PLANET_WEIGHT;
    
    public ShopGenerator(JokerRegistry jokerRegistry) {
        this.random = new Random();
        this.jokerRegistry = jokerRegistry;
    }
    
    /**
     * 生成商店物品
     */
    public Shop generateShop(GameState gameState) {
        Shop shop = new Shop();
        List<ShopItem> items = new ArrayList<>();
        
        // 生成2张随机卡牌
        for (int i = 0; i < 2; i++) {
            ShopItem item = generateRandomCard();
            if (item != null) {
                items.add(item);
            }
        }
        
        // 生成2个补充包
        for (int i = 0; i < 2; i++) {
            ShopItem boosterPack = generateBoosterPack();
            items.add(boosterPack);
        }
        
        // 生成1张优惠券
        ShopItem voucher = generateVoucher();
        items.add(voucher);
        
        shop.setItems(items);
        return shop;
    }
    
    /**
     * 重新生成随机卡牌（用于重新刷新）
     */
    public void regenerateRandomCards(Shop shop) {
        // 移除现有的随机卡牌
        shop.getItems().removeIf(item -> 
            item.getType() == ShopItemType.JOKER || 
            item.getType() == ShopItemType.TAROT || 
            item.getType() == ShopItemType.PLANET || 
            item.getType() == ShopItemType.SPECTRAL);
        
        // 生成新的随机卡牌
        for (int i = 0; i < 2; i++) {
            ShopItem item = generateRandomCard();
            if (item != null) {
                shop.getItems().add(item);
            }
        }
    }
    
    /**
     * 生成随机卡牌
     */
    private ShopItem generateRandomCard() {
        int roll = random.nextInt(TOTAL_WEIGHT);
        
        if (roll < JOKER_WEIGHT) {
            // 生成小丑牌 (71%)
            return generateJoker();
        } else if (roll < JOKER_WEIGHT + TAROT_WEIGHT) {
            // 生成塔罗牌 (14%)
            return generateTarot();
        } else {
            // 生成星球牌 (14%)
            return generatePlanet();
        }
    }
    
    /**
     * 生成小丑牌
     */
    private ShopItem generateJoker() {
        Joker joker = jokerRegistry.getRandomJoker();
        if (joker == null) return null;
        
        int price = joker.getActualCost();
        return new ShopItem(joker, ShopItemType.JOKER, price, joker.getName(), joker.getDescription());
    }
    
    /**
     * 生成塔罗牌
     */
    private ShopItem generateTarot() {
        String[] tarotNames = {
            "愚者", "魔术师", "女祭司", "皇后", "皇帝", "教皇", "恋人", "战车",
            "力量", "隐者", "命运之轮", "正义", "倒吊人", "死神", "节制", "恶魔",
            "塔", "星星", "月亮", "太阳", "审判", "世界"
        };
        
        String name = tarotNames[random.nextInt(tarotNames.length)];
        return new ShopItem(name, ShopItemType.TAROT, 3, name, "塔罗牌 - " + name);
    }
    
    /**
     * 生成星球牌
     */
    private ShopItem generatePlanet() {
        String[] planetNames = {
            "水星", "金星", "地球", "火星", "木星", "土星", "天王星", "海王星", "冥王星"
        };
        
        String name = planetNames[random.nextInt(planetNames.length)];
        return new ShopItem(name, ShopItemType.PLANET, 3, name, "星球牌 - " + name);
    }
    
    /**
     * 生成补充包
     */
    private ShopItem generateBoosterPack() {
        String[] packTypes = {"普通", "巨型", "超级"};
        int[] packPrices = {4, 6, 8};
        
        int typeIndex = random.nextInt(packTypes.length);
        String type = packTypes[typeIndex];
        int price = packPrices[typeIndex];
        
        String name = type + "补充包";
        return new ShopItem(name, ShopItemType.BOOSTER_PACK, price, name, "包含多张卡牌的补充包");
    }
    
    /**
     * 生成优惠券
     */
    private ShopItem generateVoucher() {
        String[] voucherNames = {
            "超量库存", "清仓大甩卖", "魔术技巧", "幻象", "塔罗商人", "星球商人"
        };
        
        String name = voucherNames[random.nextInt(voucherNames.length)];
        return new ShopItem(name, ShopItemType.VOUCHER, 10, name, "优惠券 - " + name);
    }
}
