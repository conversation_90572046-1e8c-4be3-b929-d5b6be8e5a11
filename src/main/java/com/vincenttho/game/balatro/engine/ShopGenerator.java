package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;

/**
 * 商店生成器
 */
public class ShopGenerator {
    private final Random random;
    private final JokerRegistry jokerRegistry;

    // 权重配置
    private static final int JOKER_WEIGHT = 20;
    private static final int TAROT_WEIGHT = 4;
    private static final int PLANET_WEIGHT = 4;
    private static final int TOTAL_WEIGHT = JOKER_WEIGHT + TAROT_WEIGHT + PLANET_WEIGHT;

    public ShopGenerator(JokerRegistry jokerRegistry) {
        this.random = new Random();
        this.jokerRegistry = jokerRegistry;
    }

    /**
     * 生成商店物品
     */
    public Shop generateShop(GameState gameState) {
        Shop shop = new Shop();
        List<ShopItem> items = new ArrayList<>();

        // 生成2张随机卡牌
        for (int i = 0; i < 2; i++) {
            ShopItem item = generateRandomCard();
            if (item != null) {
                items.add(item);
            }
        }

        // 生成2个补充包
        for (int i = 0; i < 2; i++) {
            BoosterPack boosterPack = generateBoosterPack();
            ShopItem item = new ShopItem(boosterPack, ShopItemType.BOOSTER_PACK,
                                       boosterPack.getPrice(), boosterPack.getName(),
                                       boosterPack.getType().getDescription());
            items.add(item);
        }

        // 生成1张优惠券
        ShopItem voucher = generateVoucher();
        items.add(voucher);

        shop.setItems(items);
        return shop;
    }

    /**
     * 重新生成随机卡牌（用于重新刷新）
     */
    public void regenerateRandomCards(Shop shop) {
        // 移除现有的随机卡牌
        shop.getItems().removeIf(item ->
            item.getType() == ShopItemType.JOKER ||
            item.getType() == ShopItemType.TAROT ||
            item.getType() == ShopItemType.PLANET ||
            item.getType() == ShopItemType.SPECTRAL);

        // 生成新的随机卡牌
        for (int i = 0; i < 2; i++) {
            ShopItem item = generateRandomCard();
            if (item != null) {
                shop.getItems().add(item);
            }
        }
    }

    /**
     * 生成随机卡牌
     */
    private ShopItem generateRandomCard() {
        int roll = random.nextInt(TOTAL_WEIGHT);

        if (roll < JOKER_WEIGHT) {
            // 生成小丑牌 (71%)
            return generateJoker();
        } else if (roll < JOKER_WEIGHT + TAROT_WEIGHT) {
            // 生成塔罗牌 (14%)
            return generateTarot();
        } else {
            // 生成星球牌 (14%)
            return generatePlanet();
        }
    }

    /**
     * 生成小丑牌
     */
    private ShopItem generateJoker() {
        Joker joker = jokerRegistry.getRandomJoker();
        if (joker == null) return null;

        int price = joker.getActualCost();
        return new ShopItem(joker, ShopItemType.JOKER, price, joker.getName(), joker.getDescription());
    }

    /**
     * 生成塔罗牌
     */
    private ShopItem generateTarot() {
        String[] tarotNames = {
            "愚者", "魔术师", "女祭司", "皇后", "皇帝", "教皇", "恋人", "战车",
            "力量", "隐者", "命运之轮", "正义", "倒吊人", "死神", "节制", "恶魔",
            "塔", "星星", "月亮", "太阳", "审判", "世界"
        };

        String name = tarotNames[random.nextInt(tarotNames.length)];
        return new ShopItem(name, ShopItemType.TAROT, 3, name, "塔罗牌 - " + name);
    }

    /**
     * 生成星球牌
     */
    private ShopItem generatePlanet() {
        String[] planetNames = {
            "水星", "金星", "地球", "火星", "木星", "土星", "天王星", "海王星", "冥王星"
        };

        String name = planetNames[random.nextInt(planetNames.length)];
        return new ShopItem(name, ShopItemType.PLANET, 3, name, "星球牌 - " + name);
    }

    /**
     * 生成补充包
     */
    private BoosterPack generateBoosterPack() {
        // 按权重选择补充包类型
        BoosterPackType[] types = {BoosterPackType.STANDARD, BoosterPackType.ARCANA, BoosterPackType.CELESTIAL,
                                  BoosterPackType.BUFFOON, BoosterPackType.SPECTRAL};
        double[] weights = {4.0, 4.0, 4.0, 1.2, 0.6};

        BoosterPackType type = selectByWeight(types, weights);

        // 按权重选择大小
        BoosterPackSize[] sizes = {BoosterPackSize.NORMAL, BoosterPackSize.JUMBO, BoosterPackSize.MEGA};
        double[] sizeWeights = getSizeWeights(type);

        BoosterPackSize size = selectByWeight(sizes, sizeWeights);

        // 计算价格
        int price = getBoosterPrice(size);

        // 生成名称
        String name = size.getName() + type.getName() + "包";

        return new BoosterPack(type, size, name, price);
    }

    /**
     * 根据权重选择
     */
    private <T> T selectByWeight(T[] items, double[] weights) {
        double totalWeight = 0;
        for (double weight : weights) {
            totalWeight += weight;
        }

        double randomValue = random.nextDouble() * totalWeight;
        double currentWeight = 0;

        for (int i = 0; i < items.length; i++) {
            currentWeight += weights[i];
            if (randomValue <= currentWeight) {
                return items[i];
            }
        }

        return items[items.length - 1];
    }

    /**
     * 获取大小权重
     */
    private double[] getSizeWeights(BoosterPackType type) {
        switch (type) {
            case BUFFOON:
                return new double[]{1.2, 0.6, 0.15};
            case SPECTRAL:
                return new double[]{0.6, 0.3, 0.07};
            default:
                return new double[]{4.0, 2.0, 0.5};
        }
    }

    /**
     * 获取补充包价格
     */
    private int getBoosterPrice(BoosterPackSize size) {
        switch (size) {
            case NORMAL:
                return 4;
            case JUMBO:
                return 6;
            case MEGA:
                return 8;
            default:
                return 4;
        }
    }

    /**
     * 生成优惠券
     */
    private ShopItem generateVoucher() {
        String[] voucherNames = {
            "超量库存", "清仓大甩卖", "魔术技巧", "幻象", "塔罗商人", "星球商人"
        };

        String name = voucherNames[random.nextInt(voucherNames.length)];
        return new ShopItem(name, ShopItemType.VOUCHER, 10, name, "优惠券 - " + name);
    }
}
