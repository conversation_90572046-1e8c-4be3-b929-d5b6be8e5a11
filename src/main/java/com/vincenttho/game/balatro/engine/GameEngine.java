package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 游戏引擎 - 处理核心游戏逻辑
 */
public class GameEngine {
    private GameState gameState;
    private Random random;
    private JokerRegistry jokerRegistry;
    private BlindRegistry blindRegistry;

    public GameEngine() {
        this.gameState = new GameState();
        this.random = new Random();
        this.jokerRegistry = new JokerRegistry();
        this.blindRegistry = new BlindRegistry();
        initializeGame();
    }

    /**
     * 初始化游戏
     */
    private void initializeGame() {
        // 创建标准52张牌
        createStandardDeck();

        // 洗牌并发牌
        shuffleDeck();
        dealInitialHand();

        // 设置第一个盲注
        setNextBlind();
    }

    /**
     * 创建标准牌组
     */
    private void createStandardDeck() {
        List<Card> deck = new ArrayList<>();
        for (Suit suit : Suit.values()) {
            for (Rank rank : Rank.values()) {
                deck.add(new Card(rank, suit));
            }
        }
        gameState.setDeck(deck);
    }

    /**
     * 洗牌
     */
    private void shuffleDeck() {
        Collections.shuffle(gameState.getDeck(), random);
    }

    /**
     * 发初始手牌
     */
    private void dealInitialHand() {
        List<Card> hand = new ArrayList<>();
        List<Card> deck = gameState.getDeck();

        int handSize = Math.min(gameState.getHandSize(), deck.size());
        for (int i = 0; i < handSize; i++) {
            if (!deck.isEmpty()) {
                hand.add(deck.remove(deck.size() - 1));
            }
        }

        gameState.setHand(hand);
    }

    /**
     * 设置下一个盲注
     */
    private void setNextBlind() {
        Blind blind = blindRegistry.getBlind(gameState.getAnte(), gameState.getBlindIndex());
        gameState.setCurrentBlind(blind);

        // 应用盲注效果
        if (blind != null) {
            blind.applyEffect(gameState);
        }
    }

    /**
     * 出牌
     */
    public PlayResult playHand(List<Card> selectedCards) {
        if (selectedCards.isEmpty()) {
            return new PlayResult(false, "必须选择至少一张牌", null, 0);
        }

        if (gameState.getHands() <= 0) {
            return new PlayResult(false, "没有剩余手牌次数", null, 0);
        }

        // 检查选择的牌是否都在手中
        if (!gameState.getHand().containsAll(selectedCards)) {
            return new PlayResult(false, "选择的牌不在手中", null, 0);
        }

        // 识别手牌类型
        HandResult handResult = HandEvaluator.evaluateHand(selectedCards);

        // 计算分数
        ScoreContext scoreContext = calculateScore(handResult);
        int handScore = scoreContext.calculateFinalScore();

        // 累计分数
        gameState.addCurrentScore(handScore);

        // 检查是否击败盲注
        Blind currentBlind = gameState.getCurrentBlind();
        int requiredScore = currentBlind.getRequiredScore(gameState.getAnte());
        boolean defeated = gameState.getCurrentScore() >= requiredScore;

        // 使用手牌次数
        gameState.useHand();

        // 从手中移除出牌的卡
        gameState.getHand().removeAll(selectedCards);

        // 添加到弃牌堆
        gameState.getDiscardPile().addAll(selectedCards);

        // 出牌后不自动抽牌（这是Balatro的机制）
        // 只有在弃牌时才会抽牌

        // 获得金钱奖励
        gameState.addMoney(scoreContext.getMoney());

        // 如果击败盲注
        if (defeated) {
            currentBlind.setDefeated(true);
            gameState.addMoney(currentBlind.getActualReward(gameState.getAnte()));

            // 移除盲注效果
            currentBlind.removeEffect(gameState);

            // 进入下一个盲注或Ante
            advanceGame();
        }

        // 检查是否游戏结束
        checkGameEnd();

        return new PlayResult(defeated,
                            defeated ? "击败盲注!" : "未击败盲注",
                            handResult,
                            handScore);
    }

    /**
     * 计算分数
     */
    private ScoreContext calculateScore(HandResult handResult) {
        ScoreContext context = new ScoreContext();

        // 基础分数
        context.addChips(handResult.getBaseChips());
        context.addMult(handResult.getBaseMult());

        // 计算卡牌分数
        for (Card card : handResult.getScoringCards()) {
            if (!card.isDebuffed()) {
                context.addChips(card.getBaseChips());
                context.addMult(card.getMultBonus());
                context.multiplyXMult(card.getMultMultiplier());

                // 触发小丑牌效果
                triggerJokerEffectsOnCardScored(card, context);

                // 处理印章效果
                handleSealEffects(card, context);
            }
        }

        // 触发手牌相关的小丑牌效果
        triggerJokerEffectsOnHandScored(handResult, context);

        // 应用Boss盲注效果
        Blind currentBlind = gameState.getCurrentBlind();
        if (currentBlind instanceof BossBlind) {
            BossBlind bossBlind = (BossBlind) currentBlind;
            if (bossBlind.getEffect() != null) {
                bossBlind.getEffect().modifyScore(context, handResult, gameState);
            }
        }

        return context;
    }

    /**
     * 触发卡牌计分时的小丑牌效果
     */
    private void triggerJokerEffectsOnCardScored(Card card, ScoreContext context) {
        for (Joker joker : gameState.getJokers()) {
            if (!joker.isDebuffed()) {
                JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                if (effect != null) {
                    effect.onCardScored(joker, card, context, gameState);
                }
            }
        }
    }

    /**
     * 触发手牌计分时的小丑牌效果
     */
    private void triggerJokerEffectsOnHandScored(HandResult handResult, ScoreContext context) {
        for (Joker joker : gameState.getJokers()) {
            if (!joker.isDebuffed()) {
                JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                if (effect != null) {
                    effect.onHandScored(joker, handResult, context, gameState);
                }
            }
        }
    }

    /**
     * 处理印章效果
     */
    private void handleSealEffects(Card card, ScoreContext context) {
        switch (card.getSeal()) {
            case GOLD:
                context.addMoney(3);
                break;
            case RED:
                // 重新触发此牌 - 这里简化处理
                context.addChips(card.getBaseChips());
                context.addMult(card.getMultBonus());
                context.multiplyXMult(card.getMultMultiplier());
                break;
            // 其他印章效果在其他地方处理
        }
    }

    /**
     * 弃牌
     */
    public DiscardResult discardCards(List<Card> selectedCards) {
        if (selectedCards.isEmpty()) {
            return new DiscardResult(false, "必须选择至少一张牌");
        }

        if (gameState.getDiscards() <= 0) {
            return new DiscardResult(false, "没有剩余弃牌次数");
        }

        if (!gameState.getHand().containsAll(selectedCards)) {
            return new DiscardResult(false, "选择的牌不在手中");
        }

        // 使用弃牌次数
        gameState.useDiscard();

        // 从手中移除弃牌
        gameState.getHand().removeAll(selectedCards);

        // 添加到弃牌堆
        gameState.getDiscardPile().addAll(selectedCards);

        // 触发弃牌相关的小丑牌效果
        for (Card card : selectedCards) {
            for (Joker joker : gameState.getJokers()) {
                if (!joker.isDebuffed()) {
                    JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                    if (effect != null) {
                        effect.onCardDiscarded(joker, card, gameState);
                    }
                }
            }
        }

        // 补牌
        drawCards(selectedCards.size());

        return new DiscardResult(true, "弃牌成功");
    }

    /**
     * 抽牌
     */
    private void drawCards(int count) {
        List<Card> deck = gameState.getDeck();
        List<Card> hand = gameState.getHand();

        for (int i = 0; i < count && !deck.isEmpty() && hand.size() < gameState.getHandSize(); i++) {
            hand.add(deck.remove(deck.size() - 1));
        }

        // 如果牌组空了，重新洗牌
        if (deck.isEmpty() && !gameState.getDiscardPile().isEmpty()) {
            deck.addAll(gameState.getDiscardPile());
            gameState.getDiscardPile().clear();
            shuffleDeck();
        }
    }

    /**
     * 推进游戏进度
     */
    private void advanceGame() {
        if (gameState.getBlindIndex() < 2) {
            // 进入下一个盲注
            gameState.nextBlind();
            setNextBlind();
            gameState.resetRound();

            // 补满手牌
            while (gameState.getHand().size() < gameState.getHandSize() &&
                   (!gameState.getDeck().isEmpty() || !gameState.getDiscardPile().isEmpty())) {
                drawCards(1);
            }
        } else {
            // 进入下一个Ante
            gameState.nextAnte();
            gameState.setBlindIndex(0);

            if (gameState.getAnte() > 8) {
                // 游戏胜利
                gameState.setWon(true);
                gameState.setGameOver(true);
            } else {
                setNextBlind();
                gameState.resetRound();

                // 补满手牌
                while (gameState.getHand().size() < gameState.getHandSize() &&
                       (!gameState.getDeck().isEmpty() || !gameState.getDiscardPile().isEmpty())) {
                    drawCards(1);
                }
            }
        }
    }

    /**
     * 检查游戏结束条件
     */
    private void checkGameEnd() {
        // 如果没有手牌次数且没有击败盲注
        if (gameState.getHands() <= 0 && !gameState.getCurrentBlind().isDefeated()) {
            gameState.setGameOver(true);
            gameState.setWon(false);
        }
    }

    /**
     * 跳过盲注
     */
    public boolean skipBlind() {
        if (gameState.getBlindIndex() == 2) {
            return false; // 不能跳过Boss盲注
        }

        Blind currentBlind = gameState.getCurrentBlind();
        currentBlind.removeEffect(gameState);

        advanceGame();
        return true;
    }

    // Getters
    public GameState getGameState() { return gameState; }
    public JokerRegistry getJokerRegistry() { return jokerRegistry; }
    public BlindRegistry getBlindRegistry() { return blindRegistry; }
}
