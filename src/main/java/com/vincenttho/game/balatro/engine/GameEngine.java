package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;
import com.vincenttho.game.balatro.util.HandSorter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 游戏引擎 - 处理核心游戏逻辑
 */
public class GameEngine {
    private GameState gameState;
    private Random random;
    private JokerRegistry jokerRegistry;
    private BlindRegistry blindRegistry;
    private ShopGenerator shopGenerator;
    private BoosterPackGenerator boosterPackGenerator;

    public GameEngine() {
        this.gameState = new GameState();
        this.random = new Random();
        this.jokerRegistry = new JokerRegistry();
        this.blindRegistry = new BlindRegistry();
        this.shopGenerator = new ShopGenerator(jokerRegistry);
        this.boosterPackGenerator = new BoosterPackGenerator(jokerRegistry);
        initializeGame();
    }

    /**
     * 初始化游戏
     */
    private void initializeGame() {
        // 创建标准52张牌
        createStandardDeck();

        // 洗牌并发牌
        shuffleDeck();
        dealInitialHand();

        // 设置第一个盲注
        setNextBlind();
    }

    /**
     * 创建标准牌组
     */
    private void createStandardDeck() {
        List<Card> deck = new ArrayList<>();
        for (Suit suit : Suit.values()) {
            for (Rank rank : Rank.values()) {
                deck.add(new Card(rank, suit));
            }
        }
        gameState.setDeck(deck);
    }

    /**
     * 洗牌
     */
    private void shuffleDeck() {
        Collections.shuffle(gameState.getDeck(), random);
    }

    /**
     * 发初始手牌
     */
    private void dealInitialHand() {
        List<Card> hand = new ArrayList<>();
        List<Card> deck = gameState.getDeck();

        int handSize = Math.min(gameState.getHandSize(), deck.size());
        for (int i = 0; i < handSize; i++) {
            if (!deck.isEmpty()) {
                hand.add(deck.remove(deck.size() - 1));
            }
        }

        gameState.setHand(hand);

        // 对手牌进行排序
        sortHand();
    }

    /**
     * 设置下一个盲注
     */
    private void setNextBlind() {
        Blind blind = blindRegistry.getBlind(gameState.getAnte(), gameState.getBlindIndex());
        gameState.setCurrentBlind(blind);

        // 应用盲注效果
        if (blind != null) {
            blind.applyEffect(gameState);
        }
    }

    /**
     * 出牌
     */
    public PlayResult playHand(List<Card> selectedCards) {
        if (selectedCards.isEmpty()) {
            return new PlayResult(false, "必须选择至少一张牌", null, 0);
        }

        if (gameState.getHands() <= 0) {
            return new PlayResult(false, "没有剩余手牌次数", null, 0);
        }

        // 检查选择的牌是否都在手中
        if (!gameState.getHand().containsAll(selectedCards)) {
            return new PlayResult(false, "选择的牌不在手中", null, 0);
        }

        // 识别手牌类型
        HandResult handResult = HandEvaluator.evaluateHand(selectedCards);

        // 计算分数
        ScoreContext scoreContext = calculateScore(handResult);
        int handScore = scoreContext.calculateFinalScore();

        // 累计分数
        gameState.addCurrentScore(handScore);

        // 检查是否击败盲注
        Blind currentBlind = gameState.getCurrentBlind();
        int requiredScore = currentBlind.getRequiredScore(gameState.getAnte());
        boolean defeated = gameState.getCurrentScore() >= requiredScore;

        // 使用手牌次数
        gameState.useHand();

        // 从手中移除出牌的卡
        gameState.getHand().removeAll(selectedCards);

        // 添加到弃牌堆
        gameState.getDiscardPile().addAll(selectedCards);

        // 出牌后自动抽牌补充到手牌上限
        drawToHandLimit();

        // 获得金钱奖励
        gameState.addMoney(scoreContext.getMoney());

        // 如果击败盲注
        if (defeated) {
            currentBlind.setDefeated(true);

            // 计算奖励金钱
            int rewardMoney = calculateBlindReward(currentBlind);
            gameState.addMoney(rewardMoney);

            // 移除盲注效果
            currentBlind.removeEffect(gameState);

            // 进入商店
            enterShop();
        }

        // 检查是否游戏结束
        checkGameEnd();

        return new PlayResult(defeated,
                            defeated ? "击败盲注!" : "未击败盲注",
                            handResult,
                            handScore);
    }

    /**
     * 计算分数
     */
    private ScoreContext calculateScore(HandResult handResult) {
        ScoreContext context = new ScoreContext();

        // 基础分数
        context.addChips(handResult.getBaseChips());
        context.addMult(handResult.getBaseMult());

        // 计算卡牌分数
        for (Card card : handResult.getScoringCards()) {
            if (!card.isDebuffed()) {
                context.addChips(card.getBaseChips());
                context.addMult(card.getMultBonus());
                context.multiplyXMult(card.getMultMultiplier());

                // 触发小丑牌效果
                triggerJokerEffectsOnCardScored(card, context);

                // 处理印章效果
                handleSealEffects(card, context);
            }
        }

        // 触发手牌相关的小丑牌效果
        triggerJokerEffectsOnHandScored(handResult, context);

        // 应用Boss盲注效果
        Blind currentBlind = gameState.getCurrentBlind();
        if (currentBlind instanceof BossBlind) {
            BossBlind bossBlind = (BossBlind) currentBlind;
            if (bossBlind.getEffect() != null) {
                bossBlind.getEffect().modifyScore(context, handResult, gameState);
            }
        }

        return context;
    }

    /**
     * 触发卡牌计分时的小丑牌效果
     */
    private void triggerJokerEffectsOnCardScored(Card card, ScoreContext context) {
        for (Joker joker : gameState.getJokers()) {
            if (!joker.isDebuffed()) {
                JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                if (effect != null) {
                    effect.onCardScored(joker, card, context, gameState);
                }
            }
        }
    }

    /**
     * 触发手牌计分时的小丑牌效果
     */
    private void triggerJokerEffectsOnHandScored(HandResult handResult, ScoreContext context) {
        for (Joker joker : gameState.getJokers()) {
            if (!joker.isDebuffed()) {
                JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                if (effect != null) {
                    effect.onHandScored(joker, handResult, context, gameState);
                }
            }
        }
    }

    /**
     * 处理印章效果
     */
    private void handleSealEffects(Card card, ScoreContext context) {
        switch (card.getSeal()) {
            case GOLD:
                context.addMoney(3);
                break;
            case RED:
                // 重新触发此牌 - 这里简化处理
                context.addChips(card.getBaseChips());
                context.addMult(card.getMultBonus());
                context.multiplyXMult(card.getMultMultiplier());
                break;
            // 其他印章效果在其他地方处理
        }
    }

    /**
     * 弃牌
     */
    public DiscardResult discardCards(List<Card> selectedCards) {
        if (selectedCards.isEmpty()) {
            return new DiscardResult(false, "必须选择至少一张牌");
        }

        if (gameState.getDiscards() <= 0) {
            return new DiscardResult(false, "没有剩余弃牌次数");
        }

        if (!gameState.getHand().containsAll(selectedCards)) {
            return new DiscardResult(false, "选择的牌不在手中");
        }

        // 使用弃牌次数
        gameState.useDiscard();

        // 从手中移除弃牌
        gameState.getHand().removeAll(selectedCards);

        // 添加到弃牌堆
        gameState.getDiscardPile().addAll(selectedCards);

        // 触发弃牌相关的小丑牌效果
        for (Card card : selectedCards) {
            for (Joker joker : gameState.getJokers()) {
                if (!joker.isDebuffed()) {
                    JokerEffect effect = jokerRegistry.getEffect(joker.getId());
                    if (effect != null) {
                        effect.onCardDiscarded(joker, card, gameState);
                    }
                }
            }
        }

        // 补牌
        drawCards(selectedCards.size());

        // 补牌后对手牌进行排序
        sortHand();

        return new DiscardResult(true, "弃牌成功");
    }

    /**
     * 抽牌
     */
    private void drawCards(int count) {
        List<Card> deck = gameState.getDeck();
        List<Card> hand = gameState.getHand();

        for (int i = 0; i < count && !deck.isEmpty() && hand.size() < gameState.getHandSize(); i++) {
            hand.add(deck.remove(deck.size() - 1));
        }

        // 如果牌组空了，重新洗牌
        if (deck.isEmpty() && !gameState.getDiscardPile().isEmpty()) {
            deck.addAll(gameState.getDiscardPile());
            gameState.getDiscardPile().clear();
            shuffleDeck();
        }
    }

    /**
     * 抽牌补充到手牌上限
     */
    private void drawToHandLimit() {
        List<Card> hand = gameState.getHand();
        int handSize = gameState.getHandSize();
        int cardsToDraw = handSize - hand.size();

        if (cardsToDraw > 0) {
            drawCards(cardsToDraw);
            // 抽牌后对手牌进行排序
            sortHand();
        }
    }

    /**
     * 推进游戏进度
     */
    private void advanceGame() {
        if (gameState.getBlindIndex() < 2) {
            // 进入下一个盲注
            gameState.nextBlind();
            setNextBlind();
            gameState.resetRound();

            // 补满手牌
            while (gameState.getHand().size() < gameState.getHandSize() &&
                   (!gameState.getDeck().isEmpty() || !gameState.getDiscardPile().isEmpty())) {
                drawCards(1);
            }
        } else {
            // 进入下一个Ante
            gameState.nextAnte();
            gameState.setBlindIndex(0);

            if (gameState.getAnte() > 8) {
                // 游戏胜利
                gameState.setWon(true);
                gameState.setGameOver(true);
            } else {
                setNextBlind();
                gameState.resetRound();

                // 补满手牌
                while (gameState.getHand().size() < gameState.getHandSize() &&
                       (!gameState.getDeck().isEmpty() || !gameState.getDiscardPile().isEmpty())) {
                    drawCards(1);
                }
            }
        }
    }

    /**
     * 检查游戏结束条件
     */
    private void checkGameEnd() {
        // 如果没有手牌次数且没有击败盲注
        if (gameState.getHands() <= 0 && !gameState.getCurrentBlind().isDefeated()) {
            gameState.setGameOver(true);
            gameState.setWon(false);
        }
    }

    /**
     * 跳过盲注
     */
    public boolean skipBlind() {
        if (gameState.getBlindIndex() == 2) {
            return false; // 不能跳过Boss盲注
        }

        Blind currentBlind = gameState.getCurrentBlind();
        currentBlind.removeEffect(gameState);

        advanceGame();
        return true;
    }

    /**
     * 对手牌进行排序
     */
    private void sortHand() {
        HandSorter.sortHand(gameState.getHand(), gameState.getSortType());
    }

    /**
     * 切换排序方式
     */
    public void toggleSortType() {
        gameState.toggleSortType();
        sortHand();
    }

    /**
     * 计算盲注奖励金钱
     */
    private int calculateBlindReward(Blind blind) {
        int baseReward = 0;

        // 基础奖励
        switch (blind.getType()) {
            case SMALL:
                baseReward = 3;
                break;
            case BIG:
                baseReward = 4;
                break;
            case BOSS:
                baseReward = 5;
                break;
        }

        // 剩余手牌奖励：每剩余一次手牌机会获得$1
        int handsBonus = gameState.getHands();

        // 利息：每$5获得$1利息，最高$25获得$5利息
        int interest = Math.min(gameState.getMoney() / 5, 5);

        return baseReward + handsBonus + interest;
    }

    /**
     * 进入商店
     */
    private void enterShop() {
        Shop shop = shopGenerator.generateShop(gameState);
        gameState.setCurrentShop(shop);
        gameState.enterShop();
    }

    /**
     * 退出商店
     */
    public void exitShop() {
        gameState.exitShop();
        gameState.setCurrentShop(null);

        // 进入下一个盲注或Ante
        advanceGame();
    }

    /**
     * 商店购买物品
     */
    public boolean buyShopItem(int itemIndex) {
        Shop shop = gameState.getCurrentShop();
        if (shop == null || !gameState.isInShop()) {
            return false;
        }

        List<ShopItem> items = shop.getItems();
        if (itemIndex < 0 || itemIndex >= items.size()) {
            return false;
        }

        ShopItem item = items.get(itemIndex);
        boolean success = shop.buyItem(item, gameState);

        // 如果购买的是补充包，生成内容
        if (success && item.getType() == ShopItemType.BOOSTER_PACK) {
            BoosterPack pack = gameState.getOpeningPack();
            if (pack != null) {
                boosterPackGenerator.generatePackContents(pack, gameState);
            }
        }

        return success;
    }

    /**
     * 商店重新刷新
     */
    public boolean rerollShop() {
        Shop shop = gameState.getCurrentShop();
        if (shop == null || !gameState.isInShop()) {
            return false;
        }

        if (shop.reroll(gameState)) {
            shopGenerator.regenerateRandomCards(shop);
            return true;
        }

        return false;
    }

    /**
     * 选择补充包内容
     */
    public boolean selectBoosterPackItems(List<Integer> selectedIndices) {
        BoosterPack pack = gameState.getOpeningPack();
        if (pack == null) {
            return false;
        }

        // 检查选择数量
        if (selectedIndices.size() > pack.getMaxSelections()) {
            return false;
        }

        List<Object> contents = pack.getContents();

        // 应用选中的物品
        for (int index : selectedIndices) {
            if (index >= 0 && index < contents.size()) {
                Object item = contents.get(index);
                applyBoosterItem(item);
            }
        }

        // 清理补充包状态
        gameState.setOpeningPack(null);
        gameState.getBoosterHand().clear();

        return true;
    }

    /**
     * 跳过补充包
     */
    public void skipBoosterPack() {
        gameState.setOpeningPack(null);
        gameState.getBoosterHand().clear();
    }

    /**
     * 应用补充包物品
     */
    private void applyBoosterItem(Object item) {
        if (item instanceof Card) {
            // 标准卡牌添加到牌组
            Card card = (Card) item;
            gameState.getDeck().add(card);
        } else if (item instanceof Joker) {
            // 小丑牌添加到小丑槽位
            Joker joker = (Joker) item;
            gameState.addJoker(joker);
        } else if (item instanceof TarotCard) {
            // 塔罗牌添加到库存
            TarotCard tarot = (TarotCard) item;
            gameState.addTarotCard(tarot);
        } else if (item instanceof PlanetCard) {
            // 星球牌添加到库存
            PlanetCard planet = (PlanetCard) item;
            gameState.addPlanetCard(planet);
        } else if (item instanceof SpectralCard) {
            // 幽灵牌添加到库存
            SpectralCard spectral = (SpectralCard) item;
            gameState.addSpectralCard(spectral);
        }
    }

    /**
     * 应用塔罗牌效果
     */
    private void applyTarotEffect(TarotCard tarot, List<Card> targetCards) {
        // 简化实现：根据塔罗牌类型应用不同效果
        if (targetCards.isEmpty()) {
            targetCards = gameState.getHand(); // 如果没有指定目标，使用所有手牌
        }

        switch (tarot.getName()) {
            case "愚者":
                // 随机增强一张卡
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    Enhancement[] enhancements = Enhancement.values();
                    randomCard.setEnhancement(enhancements[random.nextInt(enhancements.length)]);
                }
                break;
            case "魔术师":
                // 添加版本
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    Edition[] editions = {Edition.FOIL, Edition.HOLOGRAPHIC, Edition.POLYCHROME};
                    randomCard.setEdition(editions[random.nextInt(editions.length)]);
                }
                break;
            default:
                // 默认效果：随机增强
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    Enhancement[] enhancements = Enhancement.values();
                    randomCard.setEnhancement(enhancements[random.nextInt(enhancements.length)]);
                }
                break;
        }
    }

    /**
     * 应用星球牌效果
     */
    private void applyPlanetEffect(PlanetCard planet) {
        // 简化实现：随机升级一个手牌类型
        List<PokerHand> unlockedHands = gameState.getUnlockedHands();
        if (!unlockedHands.isEmpty()) {
            PokerHand randomHand = unlockedHands.get(random.nextInt(unlockedHands.size()));
            randomHand.levelUp();
        }
    }

    /**
     * 应用幽灵牌效果
     */
    private void applySpectralEffect(SpectralCard spectral, List<Card> targetCards) {
        // 简化实现：根据幽灵牌类型应用不同效果
        if (targetCards.isEmpty()) {
            targetCards = gameState.getHand(); // 如果没有指定目标，使用所有手牌
        }

        switch (spectral.getName()) {
            case "幽灵":
                // 添加版本
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    Edition[] editions = {Edition.FOIL, Edition.HOLOGRAPHIC, Edition.POLYCHROME};
                    randomCard.setEdition(editions[random.nextInt(editions.length)]);
                }
                break;
            case "迷雾":
                // 添加印章
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    Seal[] seals = {Seal.GOLD, Seal.RED, Seal.BLUE, Seal.PURPLE};
                    randomCard.setSeal(seals[random.nextInt(seals.length)]);
                }
                break;
            default:
                // 默认效果：随机添加版本或印章
                if (!targetCards.isEmpty()) {
                    Card randomCard = targetCards.get(random.nextInt(targetCards.size()));
                    if (random.nextBoolean()) {
                        // 添加版本
                        Edition[] editions = {Edition.FOIL, Edition.HOLOGRAPHIC, Edition.POLYCHROME};
                        randomCard.setEdition(editions[random.nextInt(editions.length)]);
                    } else {
                        // 添加印章
                        Seal[] seals = {Seal.GOLD, Seal.RED, Seal.BLUE, Seal.PURPLE};
                        randomCard.setSeal(seals[random.nextInt(seals.length)]);
                    }
                }
                break;
        }
    }

    /**
     * 使用塔罗牌
     */
    public boolean useTarotCard(int index, List<Integer> targetCardIndices) {
        List<TarotCard> tarotCards = gameState.getTarotCards();
        if (index < 0 || index >= tarotCards.size()) {
            return false;
        }

        TarotCard tarot = tarotCards.get(index);

        // 获取目标卡牌
        List<Card> targetCards = new ArrayList<>();
        for (int cardIndex : targetCardIndices) {
            if (cardIndex >= 0 && cardIndex < gameState.getHand().size()) {
                targetCards.add(gameState.getHand().get(cardIndex));
            }
        }

        // 应用塔罗牌效果
        applyTarotEffect(tarot, targetCards);

        // 移除已使用的塔罗牌
        tarotCards.remove(index);

        return true;
    }

    /**
     * 使用星球牌
     */
    public boolean usePlanetCard(int index) {
        List<PlanetCard> planetCards = gameState.getPlanetCards();
        if (index < 0 || index >= planetCards.size()) {
            return false;
        }

        PlanetCard planet = planetCards.get(index);

        // 应用星球牌效果
        applyPlanetEffect(planet);

        // 移除已使用的星球牌
        planetCards.remove(index);

        return true;
    }

    /**
     * 使用幽灵牌
     */
    public boolean useSpectralCard(int index, List<Integer> targetCardIndices) {
        List<SpectralCard> spectralCards = gameState.getSpectralCards();
        if (index < 0 || index >= spectralCards.size()) {
            return false;
        }

        SpectralCard spectral = spectralCards.get(index);

        // 获取目标卡牌
        List<Card> targetCards = new ArrayList<>();
        for (int cardIndex : targetCardIndices) {
            if (cardIndex >= 0 && cardIndex < gameState.getHand().size()) {
                targetCards.add(gameState.getHand().get(cardIndex));
            }
        }

        // 应用幽灵牌效果
        applySpectralEffect(spectral, targetCards);

        // 移除已使用的幽灵牌
        spectralCards.remove(index);

        return true;
    }

    // Getters
    public GameState getGameState() { return gameState; }
    public JokerRegistry getJokerRegistry() { return jokerRegistry; }
    public BlindRegistry getBlindRegistry() { return blindRegistry; }
}
