package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.List;

/**
 * 小丑牌效果接口
 */
public interface JokerEffect {
    
    /**
     * 当手牌被选择时触发
     */
    default void onHandSelected(Joker joker, List<Card> hand, GameState gameState) {}
    
    /**
     * 当卡牌计分时触发
     */
    default void onCardScored(Joker joker, Card card, ScoreContext context, GameState gameState) {}
    
    /**
     * 当手牌计分时触发（独立于卡牌）
     */
    default void onHandScored(Joker joker, HandResult handResult, ScoreContext context, GameState gameState) {}
    
    /**
     * 当卡牌被保留在手中时触发
     */
    default void onCardHeld(Joker joker, Card card, GameState gameState) {}
    
    /**
     * 当卡牌被弃掉时触发
     */
    default void onCardDiscarded(Joker joker, Card card, GameState gameState) {}
    
    /**
     * 当盲注被选择时触发
     */
    default void onBlindSelected(Joker joker, Blind blind, GameState gameState) {}
    
    /**
     * 当回合开始时触发
     */
    default void onRoundStart(Joker joker, GameState gameState) {}
    
    /**
     * 当回合结束时触发
     */
    default void onRoundEnd(Joker joker, GameState gameState) {}
    
    /**
     * 当商店访问时触发
     */
    default void onShopVisit(Joker joker, GameState gameState) {}
    
    /**
     * 获取小丑牌的描述文本
     */
    default String getDescription(Joker joker) {
        return joker.getDescription();
    }
}
