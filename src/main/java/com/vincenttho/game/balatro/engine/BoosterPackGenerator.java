package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;

/**
 * 补充包内容生成器
 */
public class BoosterPackGenerator {
    private final Random random;
    private final JokerRegistry jokerRegistry;

    public BoosterPackGenerator(JokerRegistry jokerRegistry) {
        this.random = new Random();
        this.jokerRegistry = jokerRegistry;
    }

    /**
     * 生成补充包内容
     */
    public void generatePackContents(BoosterPack pack, GameState gameState) {
        List<Object> contents = new ArrayList<>();
        int maxOptions = pack.getMaxOptions();

        switch (pack.getType()) {
            case STANDARD:
                contents = generateStandardCards(maxOptions);
                break;
            case ARCANA:
                contents = generateTarotCards(maxOptions);
                // 为塔罗牌包发一手牌
                dealBoosterHand(gameState);
                break;
            case CELESTIAL:
                contents = generatePlanetCards(maxOptions);
                break;
            case BUFFOON:
                contents = generateJokerCards(maxOptions);
                break;
            case SPECTRAL:
                contents = generateSpectralCards(maxOptions);
                // 为幽灵牌包发一手牌
                dealBoosterHand(gameState);
                break;
        }

        pack.setContents(contents);
    }

    /**
     * 为补充包发一手牌（塔罗牌和幽灵牌需要）
     */
    private void dealBoosterHand(GameState gameState) {
        List<Card> boosterHand = new ArrayList<>();
        List<Card> deck = gameState.getDeck();

        // 从牌组中发5张牌
        int cardsToDeal = Math.min(5, deck.size());
        for (int i = 0; i < cardsToDeal; i++) {
            if (!deck.isEmpty()) {
                boosterHand.add(deck.remove(deck.size() - 1));
            }
        }

        gameState.setBoosterHand(boosterHand);
    }

    /**
     * 生成标准扑克牌
     */
    private List<Object> generateStandardCards(int count) {
        List<Object> cards = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            // 随机生成扑克牌，可能有增强、版本、印章
            Rank rank = Rank.values()[random.nextInt(Rank.values().length)];
            Suit suit = Suit.values()[random.nextInt(Suit.values().length)];
            Card card = new Card(rank, suit);

            // 30%概率有增强
            if (random.nextDouble() < 0.3) {
                Enhancement[] enhancements = Enhancement.values();
                card.setEnhancement(enhancements[random.nextInt(enhancements.length)]);
            }

            // 10%概率有版本
            if (random.nextDouble() < 0.1) {
                Edition[] editions = {Edition.FOIL, Edition.HOLOGRAPHIC, Edition.POLYCHROME};
                card.setEdition(editions[random.nextInt(editions.length)]);
            }

            // 15%概率有印章
            if (random.nextDouble() < 0.15) {
                Seal[] seals = {Seal.GOLD, Seal.RED, Seal.BLUE, Seal.PURPLE};
                card.setSeal(seals[random.nextInt(seals.length)]);
            }

            cards.add(card);
        }

        return cards;
    }

    /**
     * 生成塔罗牌
     */
    private List<Object> generateTarotCards(int count) {
        List<Object> cards = new ArrayList<>();
        String[] tarotNames = {
            "愚者", "魔术师", "女祭司", "皇后", "皇帝", "教皇", "恋人", "战车",
            "力量", "隐者", "命运之轮", "正义", "倒吊人", "死神", "节制", "恶魔",
            "塔", "星星", "月亮", "太阳", "审判", "世界"
        };

        for (int i = 0; i < count; i++) {
            String name = tarotNames[random.nextInt(tarotNames.length)];
            cards.add(new TarotCard(name, "塔罗牌效果：" + name));
        }

        return cards;
    }

    /**
     * 生成星球牌
     */
    private List<Object> generatePlanetCards(int count) {
        List<Object> cards = new ArrayList<>();
        String[] planetNames = {
            "水星", "金星", "地球", "火星", "木星", "土星", "天王星", "海王星", "冥王星"
        };

        for (int i = 0; i < count; i++) {
            String name = planetNames[random.nextInt(planetNames.length)];
            cards.add(new PlanetCard(name, "升级手牌类型：" + name));
        }

        return cards;
    }

    /**
     * 生成小丑牌
     */
    private List<Object> generateJokerCards(int count) {
        List<Object> cards = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            Joker joker = jokerRegistry.getRandomJoker();
            if (joker != null) {
                cards.add(joker);
            }
        }

        return cards;
    }

    /**
     * 生成幽灵牌
     */
    private List<Object> generateSpectralCards(int count) {
        List<Object> cards = new ArrayList<>();
        String[] spectralNames = {
            "幻象", "灵魂", "黑洞", "白洞", "迷雾", "阴霾", "咒语", "诅咒"
        };

        for (int i = 0; i < count; i++) {
            String name = spectralNames[random.nextInt(spectralNames.length)];
            cards.add(new SpectralCard(name, "幽灵牌效果：" + name));
        }

        return cards;
    }
}
