package com.vincenttho.game.balatro.engine;

import com.vincenttho.game.balatro.model.*;

import java.util.*;

/**
 * 盲注注册表
 */
public class BlindRegistry {
    private final Map<String, Blind> smallBlinds;
    private final Map<String, Blind> bigBlinds;
    private final Map<String, BossBlind> bossBlinds;
    private final Random random;
    
    public BlindRegistry() {
        this.smallBlinds = new HashMap<>();
        this.bigBlinds = new HashMap<>();
        this.bossBlinds = new HashMap<>();
        this.random = new Random();
        initializeBlinds();
    }
    
    /**
     * 初始化所有盲注
     */
    private void initializeBlinds() {
        // 小盲注
        smallBlinds.put("small_blind", new Blind("小盲注", "基础盲注", BlindType.SMALL, 300, 1));
        
        // 大盲注
        bigBlinds.put("big_blind", new Blind("大盲注", "基础盲注", BlindType.BIG, 800, 2));
        
        // Boss盲注
        initializeBossBlinds();
    }
    
    /**
     * 初始化Boss盲注
     */
    private void initializeBossBlinds() {
        // The Hook - 每回合弃掉2张随机牌
        bossBlinds.put("the_hook", new BossBlind("钩子", "每回合弃掉2张随机牌", 
                                                "每回合弃掉2张随机牌", 2000, 5, new TheHookEffect()));
        
        // The Ox - 出牌的手牌类型设为最常出的手牌类型
        bossBlinds.put("the_ox", new BossBlind("公牛", "出牌的手牌类型设为最常出的手牌类型",
                                              "出牌的手牌类型设为最常出的手牌类型", 2000, 5, new TheOxEffect()));
        
        // The House - 第一次出牌时，降低手牌类型等级
        bossBlinds.put("the_house", new BossBlind("房子", "第一次出牌时，降低手牌类型等级",
                                                 "第一次出牌时，降低手牌类型等级", 2000, 5, new TheHouseEffect()));
        
        // The Wall - 大盲注的2倍分数
        bossBlinds.put("the_wall", new BossBlind("墙", "大盲注的2倍分数", 
                                                "大盲注的2倍分数", 4000, 5, null));
        
        // The Wheel - 1/7概率弃掉随机小丑牌
        bossBlinds.put("the_wheel", new BossBlind("轮子", "1/7概率弃掉随机小丑牌",
                                                 "1/7概率弃掉随机小丑牌", 2000, 5, new TheWheelEffect()));
        
        // The Arm - 降低出牌手牌类型等级
        bossBlinds.put("the_arm", new BossBlind("手臂", "降低出牌手牌类型等级",
                                               "降低出牌手牌类型等级", 2000, 5, new TheArmEffect()));
        
        // The Club - 所有梅花失效
        bossBlinds.put("the_club", new BossBlind("梅花", "所有梅花失效",
                                                "所有梅花失效", 2000, 5, new TheClubEffect()));
        
        // The Fish - 每次出牌后洗牌
        bossBlinds.put("the_fish", new BossBlind("鱼", "每次出牌后洗牌",
                                                "每次出牌后洗牌", 2000, 5, new TheFishEffect()));
        
        // The Psychic - 必须出5张牌
        bossBlinds.put("the_psychic", new BossBlind("通灵师", "必须出5张牌",
                                                   "必须出5张牌", 2000, 5, new ThePsychicEffect()));
        
        // The Goad - 所有黑桃失效
        bossBlinds.put("the_goad", new BossBlind("刺激", "所有黑桃失效",
                                                "所有黑桃失效", 2000, 5, new TheGoadEffect()));
        
        // The Water - 开始时只有1次弃牌
        bossBlinds.put("the_water", new BossBlind("水", "开始时只有1次弃牌",
                                                 "开始时只有1次弃牌", 2000, 5, new TheWaterEffect()));
        
        // The Window - 所有方块失效
        bossBlinds.put("the_window", new BossBlind("窗户", "所有方块失效",
                                                  "所有方块失效", 2000, 5, new TheWindowEffect()));
        
        // The Manacle - 开始时只有1次出牌
        bossBlinds.put("the_manacle", new BossBlind("手铐", "开始时只有1次出牌",
                                                   "开始时只有1次出牌", 2000, 5, new TheManacleEffect()));
        
        // The Eye - 不能重复出牌类型
        bossBlinds.put("the_eye", new BossBlind("眼睛", "不能重复出牌类型",
                                               "不能重复出牌类型", 2000, 5, new TheEyeEffect()));
        
        // The Mouth - 只能出一种手牌类型
        bossBlinds.put("the_mouth", new BossBlind("嘴巴", "只能出一种手牌类型",
                                                 "只能出一种手牌类型", 2000, 5, new TheMouthEffect()));
        
        // The Plant - 所有人头牌失效
        bossBlinds.put("the_plant", new BossBlind("植物", "所有人头牌失效",
                                                 "所有人头牌失效", 2000, 5, new ThePlantEffect()));
        
        // The Serpent - 总是从3张牌开始
        bossBlinds.put("the_serpent", new BossBlind("蛇", "总是从3张牌开始",
                                                   "总是从3张牌开始", 2000, 5, new TheSerpentEffect()));
        
        // The Pillar - 出牌时已出过的牌失效
        bossBlinds.put("the_pillar", new BossBlind("柱子", "出牌时已出过的牌失效",
                                                  "出牌时已出过的牌失效", 2000, 5, new ThePillarEffect()));
        
        // The Needle - 只能出1张牌
        bossBlinds.put("the_needle", new BossBlind("针", "只能出1张牌",
                                                  "只能出1张牌", 2000, 5, new TheNeedleEffect()));
        
        // The Head - 所有红桃失效
        bossBlinds.put("the_head", new BossBlind("头", "所有红桃失效",
                                                "所有红桃失效", 2000, 5, new TheHeadEffect()));
        
        // The Tooth - 每次出牌失去$1
        bossBlinds.put("the_tooth", new BossBlind("牙齿", "每次出牌失去$1",
                                                 "每次出牌失去$1", 2000, 5, new TheToothEffect()));
    }
    
    /**
     * 获取指定Ante和盲注索引的盲注
     */
    public Blind getBlind(int ante, int blindIndex) {
        switch (blindIndex) {
            case 0:
                return smallBlinds.get("small_blind");
            case 1:
                return bigBlinds.get("big_blind");
            case 2:
                return getRandomBossBlind();
            default:
                return null;
        }
    }
    
    /**
     * 获取随机Boss盲注
     */
    private BossBlind getRandomBossBlind() {
        List<BossBlind> bossList = new ArrayList<>(bossBlinds.values());
        return bossList.get(random.nextInt(bossList.size()));
    }
    
    // Boss盲注效果实现
    
    private static class TheHookEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 每回合开始时弃掉2张随机牌
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class TheOxEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 记录最常出的手牌类型
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class TheHouseEffect implements BossBlindEffect {
        private boolean firstHand = true;
        
        @Override
        public void apply(GameState gameState) {
            firstHand = true;
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
        
        @Override
        public void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {
            if (firstHand) {
                handResult.getHandType().levelDown();
                firstHand = false;
            }
        }
    }
    
    private static class TheWheelEffect implements BossBlindEffect {
        private final Random random = new Random();
        
        @Override
        public void apply(GameState gameState) {
            // 1/7概率弃掉随机小丑牌
            if (random.nextInt(7) == 0 && !gameState.getJokers().isEmpty()) {
                List<Joker> jokers = gameState.getJokers();
                Joker randomJoker = jokers.get(random.nextInt(jokers.size()));
                if (!randomJoker.isEternal()) {
                    gameState.removeJoker(randomJoker);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class TheArmEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 无需应用时效果
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
        
        @Override
        public void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {
            handResult.getHandType().levelDown();
        }
    }
    
    private static class TheClubEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 所有梅花失效
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.CLUBS) {
                    card.setDebuffed(true);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复梅花
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.CLUBS) {
                    card.setDebuffed(false);
                }
            }
        }
    }
    
    private static class TheFishEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 每次出牌后洗牌 - 在游戏引擎中处理
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class ThePsychicEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 必须出5张牌 - 在游戏引擎中检查
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class TheGoadEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 所有黑桃失效
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.SPADES) {
                    card.setDebuffed(true);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复黑桃
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.SPADES) {
                    card.setDebuffed(false);
                }
            }
        }
    }
    
    private static class TheWaterEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            gameState.setDiscards(1);
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复正常弃牌数在下一回合
        }
    }
    
    private static class TheWindowEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 所有方块失效
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.DIAMONDS) {
                    card.setDebuffed(true);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复方块
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.DIAMONDS) {
                    card.setDebuffed(false);
                }
            }
        }
    }
    
    private static class TheManacleEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            gameState.setHands(1);
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复正常手牌数在下一回合
        }
    }
    
    private static class TheEyeEffect implements BossBlindEffect {
        private final Set<PokerHand> playedHands = new HashSet<>();
        
        @Override
        public void apply(GameState gameState) {
            playedHands.clear();
        }
        
        @Override
        public void remove(GameState gameState) {
            playedHands.clear();
        }
        
        @Override
        public boolean triggersEffect(HandResult handResult, GameState gameState) {
            return playedHands.contains(handResult.getHandType());
        }
        
        @Override
        public void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {
            playedHands.add(handResult.getHandType());
        }
    }
    
    private static class TheMouthEffect implements BossBlindEffect {
        private PokerHand allowedHand = null;
        
        @Override
        public void apply(GameState gameState) {
            allowedHand = null;
        }
        
        @Override
        public void remove(GameState gameState) {
            allowedHand = null;
        }
        
        @Override
        public boolean triggersEffect(HandResult handResult, GameState gameState) {
            if (allowedHand == null) {
                allowedHand = handResult.getHandType();
                return false;
            }
            return handResult.getHandType() != allowedHand;
        }
    }
    
    private static class ThePlantEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 所有人头牌失效
            for (Card card : gameState.getHand()) {
                if (card.getRank().isFaceCard()) {
                    card.setDebuffed(true);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复人头牌
            for (Card card : gameState.getHand()) {
                if (card.getRank().isFaceCard()) {
                    card.setDebuffed(false);
                }
            }
        }
    }
    
    private static class TheSerpentEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 总是从3张牌开始 - 在游戏引擎中处理
            while (gameState.getHand().size() > 3) {
                Card card = gameState.getHand().remove(gameState.getHand().size() - 1);
                gameState.getDiscardPile().add(card);
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class ThePillarEffect implements BossBlindEffect {
        private final Set<Card> playedCards = new HashSet<>();
        
        @Override
        public void apply(GameState gameState) {
            playedCards.clear();
        }
        
        @Override
        public void remove(GameState gameState) {
            playedCards.clear();
        }
        
        @Override
        public void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {
            // 已出过的牌失效
            for (Card card : handResult.getScoringCards()) {
                if (playedCards.contains(card)) {
                    card.setDebuffed(true);
                } else {
                    playedCards.add(card);
                }
            }
        }
    }
    
    private static class TheNeedleEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 只能出1张牌 - 在游戏引擎中检查
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
    }
    
    private static class TheHeadEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 所有红桃失效
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.HEARTS) {
                    card.setDebuffed(true);
                }
            }
        }
        
        @Override
        public void remove(GameState gameState) {
            // 恢复红桃
            for (Card card : gameState.getHand()) {
                if (card.getSuit() == Suit.HEARTS) {
                    card.setDebuffed(false);
                }
            }
        }
    }
    
    private static class TheToothEffect implements BossBlindEffect {
        @Override
        public void apply(GameState gameState) {
            // 每次出牌失去$1 - 在游戏引擎中处理
        }
        
        @Override
        public void remove(GameState gameState) {
            // 无需移除效果
        }
        
        @Override
        public void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {
            gameState.addMoney(-1);
        }
    }
}
