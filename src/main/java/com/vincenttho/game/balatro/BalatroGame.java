package com.vincenttho.game.balatro;

import com.vincenttho.game.balatro.ui.GameUI;

/**
 * Balatro游戏主类
 */
public class BalatroGame {
    
    public static void main(String[] args) {
        try {
            GameUI gameUI = new GameUI();
            gameUI.start();
        } catch (Exception e) {
            System.err.println("游戏运行时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
