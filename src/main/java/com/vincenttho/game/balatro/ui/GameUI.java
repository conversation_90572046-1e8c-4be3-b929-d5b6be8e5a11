package com.vincenttho.game.balatro.ui;

import com.vincenttho.game.balatro.engine.GameEngine;
import com.vincenttho.game.balatro.model.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 游戏用户界面
 */
public class GameUI {
    private final GameEngine gameEngine;
    private final Scanner scanner;

    public GameUI() {
        this.gameEngine = new GameEngine();
        this.scanner = new Scanner(System.in);
    }

    /**
     * 重复字符串的辅助方法 (Java 8兼容)
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 启动游戏
     */
    public void start() {
        System.out.println(repeatString("=", 60));
        System.out.println("欢迎来到 Balatro - Java命令行版本!");
        System.out.println(repeatString("=", 60));
        System.out.println();

        showInstructions();

        while (!gameEngine.getGameState().isGameOver()) {
            if (gameEngine.getGameState().isOpeningPack()) {
                showBoosterPack();
                showBoosterMenu();
            } else if (gameEngine.getGameState().isInShop()) {
                showShop();
                showShopMenu();
            } else {
                showGameState();
                showHand();
                showJokersAndConsumables();
                showMenu();
            }

            String input = scanner.nextLine().trim().toLowerCase();
            handleInput(input);

            System.out.println();
        }

        showGameEnd();
    }

    /**
     * 显示游戏说明
     */
    private void showInstructions() {
        System.out.println("游戏说明:");
        System.out.println("- 这是一个基于扑克的Roguelike卡牌构建游戏");
        System.out.println("- 通过出牌获得分数来击败盲注");
        System.out.println("- 每个Ante有3个盲注：小盲注、大盲注、Boss盲注");
        System.out.println("- 击败8个Ante即可获胜");
        System.out.println("- 使用小丑牌来增强你的分数");
        System.out.println();
        System.out.println("命令:");
        System.out.println("- play [卡牌编号...] : 出牌 (例: play 1 2 3)");
        System.out.println("- discard [卡牌编号...] : 弃牌 (例: discard 1 2)");
        System.out.println("- tarot [T编号] [手牌编号...] : 使用塔罗牌 (例: tarot T1 1 2)");
        System.out.println("- planet [P编号] : 使用星球牌 (例: planet P1)");
        System.out.println("- spectral [S编号] [手牌编号...] : 使用幽灵牌 (例: spectral S1 1)");
        System.out.println("- sort : 切换手牌排序方式 (点数/花色)");
        System.out.println("- skip : 跳过当前盲注 (不能跳过Boss盲注)");
        System.out.println("- info : 显示详细信息");
        System.out.println("- help : 显示帮助");
        System.out.println("- quit : 退出游戏");
        System.out.println();
        System.out.println("按回车键开始游戏...");
        scanner.nextLine();
    }

    /**
     * 显示游戏状态
     */
    private void showGameState() {
        GameState state = gameEngine.getGameState();
        Blind currentBlind = state.getCurrentBlind();

        // 精简显示：一行显示所有关键信息
        if (currentBlind != null) {
            int requiredScore = currentBlind.getRequiredScore(state.getAnte());
            int currentScore = state.getCurrentScore();
            String bossInfo = (currentBlind instanceof BossBlind) ? " [Boss]" : "";
            System.out.println(String.format("A%d %s%s | 目标:%d 当前:%d | $%d | 手:%d 弃:%d",
                state.getAnte(), currentBlind.getName(), bossInfo,
                requiredScore, currentScore, state.getMoney(), state.getHands(), state.getDiscards()));
        }

        // 显示小丑牌
        if (!state.getJokers().isEmpty()) {
            System.out.println();
            System.out.println("小丑牌:");
            for (int i = 0; i < state.getJokers().size(); i++) {
                Joker joker = state.getJokers().get(i);
                System.out.println(String.format("  %d. %s - %s", i + 1, joker.toString(), joker.getDescription()));
            }
        }

        System.out.println();
    }

    /**
     * 显示手牌
     */
    private void showHand() {
        GameState state = gameEngine.getGameState();
        List<Card> hand = state.getHand();

        // 精简手牌显示：一行显示所有手牌
        if (hand.isEmpty()) {
            System.out.println("手牌: (无)");
        } else {
            StringBuilder handStr = new StringBuilder("手牌: ");
            for (int i = 0; i < hand.size(); i++) {
                Card card = hand.get(i);
                handStr.append(String.format("%d.%s ", i + 1, card.toString()));
            }
            System.out.println(handStr.toString().trim());
        }
    }

    /**
     * 显示小丑牌和消耗品
     */
    private void showJokersAndConsumables() {
        GameState state = gameEngine.getGameState();

        // 精简显示小丑牌和消耗品
        List<Joker> jokers = state.getJokers();
        List<TarotCard> tarotCards = state.getTarotCards();
        List<PlanetCard> planetCards = state.getPlanetCards();
        List<SpectralCard> spectralCards = state.getSpectralCards();

        StringBuilder itemsStr = new StringBuilder();

        // 小丑牌
        if (!jokers.isEmpty()) {
            itemsStr.append("小丑: ");
            for (int i = 0; i < jokers.size(); i++) {
                itemsStr.append(String.format("%s ", jokers.get(i).getName()));
            }
        }

        // 消耗品
        if (!tarotCards.isEmpty() || !planetCards.isEmpty() || !spectralCards.isEmpty()) {
            if (itemsStr.length() > 0) itemsStr.append("| ");
            itemsStr.append("消耗: ");

            for (int i = 0; i < tarotCards.size(); i++) {
                itemsStr.append(String.format("T%d.%s ", i + 1, tarotCards.get(i).getName()));
            }
            for (int i = 0; i < planetCards.size(); i++) {
                itemsStr.append(String.format("P%d.%s ", i + 1, planetCards.get(i).getName()));
            }
            for (int i = 0; i < spectralCards.size(); i++) {
                itemsStr.append(String.format("S%d.%s ", i + 1, spectralCards.get(i).getName()));
            }
        }

        if (itemsStr.length() > 0) {
            System.out.println(itemsStr.toString().trim());
        }
    }

    /**
     * 显示菜单
     */
    private void showMenu() {
        // 精简菜单：一行显示主要命令
        System.out.print("命令: play/discard/tarot/planet/spectral/sort/skip/quit > ");
    }

    /**
     * 显示商店
     */
    private void showShop() {
        GameState state = gameEngine.getGameState();
        Shop shop = state.getCurrentShop();

        // 精简商店显示
        System.out.println(String.format("🏦商店 | $%d | 刷新:$%d", state.getMoney(), shop != null ? shop.getRerollCost() : 5));

        if (shop != null && !shop.getItems().isEmpty()) {
            StringBuilder itemsStr = new StringBuilder("商品: ");
            for (int i = 0; i < shop.getItems().size(); i++) {
                ShopItem item = shop.getItems().get(i);
                itemsStr.append(String.format("%d.%s($%d) ", i + 1, item.getName(), item.getPrice()));
            }
            System.out.println(itemsStr.toString().trim());
        }
    }

    /**
     * 显示商店菜单
     */
    private void showShopMenu() {
        System.out.print("命令: buy/reroll/leave/quit > ");
    }

    /**
     * 显示补充包
     */
    private void showBoosterPack() {
        GameState state = gameEngine.getGameState();
        BoosterPack pack = state.getOpeningPack();

        // 精简补充包显示
        System.out.println(String.format("🎁%s | 最多选%d张", pack.getName(), pack.getMaxSelections()));

        // 显示选项
        if (pack.getContents() != null && !pack.getContents().isEmpty()) {
            StringBuilder optionsStr = new StringBuilder("选项: ");
            for (int i = 0; i < pack.getContents().size(); i++) {
                Object item = pack.getContents().get(i);
                String itemName = getItemName(item);
                optionsStr.append(String.format("%d.%s ", i + 1, itemName));
            }
            System.out.println(optionsStr.toString().trim());
        }

        // 如果有手牌（塔罗牌/幽灵牌包）
        if (!state.getBoosterHand().isEmpty()) {
            StringBuilder handStr = new StringBuilder("手牌: ");
            for (int i = 0; i < state.getBoosterHand().size(); i++) {
                Card card = state.getBoosterHand().get(i);
                handStr.append(String.format("%s ", card.toString()));
            }
            System.out.println(handStr.toString().trim());
        }
    }

    /**
     * 获取物品描述
     */
    private String getItemDescription(Object item) {
        if (item instanceof Card) {
            Card card = (Card) item;
            return card.toString() + " (" + card.getBaseChips() + "筹码)";
        } else if (item instanceof Joker) {
            Joker joker = (Joker) item;
            return joker.getName() + " - " + joker.getDescription();
        } else if (item instanceof TarotCard) {
            TarotCard tarot = (TarotCard) item;
            return tarot.getName() + " - " + tarot.getDescription();
        } else if (item instanceof PlanetCard) {
            PlanetCard planet = (PlanetCard) item;
            return planet.getName() + " - " + planet.getDescription();
        } else if (item instanceof SpectralCard) {
            SpectralCard spectral = (SpectralCard) item;
            return spectral.getName() + " - " + spectral.getDescription();
        }
        return item.toString();
    }

    /**
     * 获取物品名称（精简版）
     */
    private String getItemName(Object item) {
        if (item instanceof Card) {
            return ((Card) item).toString();
        } else if (item instanceof Joker) {
            return ((Joker) item).getName();
        } else if (item instanceof TarotCard) {
            return ((TarotCard) item).getName();
        } else if (item instanceof PlanetCard) {
            return ((PlanetCard) item).getName();
        } else if (item instanceof SpectralCard) {
            return ((SpectralCard) item).getName();
        }
        return item.toString();
    }

    /**
     * 显示补充包菜单
     */
    private void showBoosterMenu() {
        BoosterPack pack = gameEngine.getGameState().getOpeningPack();
        System.out.print(String.format("命令: select(最多%d张)/skip/quit > ", pack.getMaxSelections()));
    }

    /**
     * 处理用户输入
     */
    private void handleInput(String input) {
        String[] parts = input.split("\\s+");
        String command = parts[0];

        // 判断当前状态
        if (gameEngine.getGameState().isOpeningPack()) {
            handleBoosterInput(command, parts);
        } else if (gameEngine.getGameState().isInShop()) {
            handleShopInput(command, parts);
        } else {
            handleGameInput(command, parts);
        }
    }

    /**
     * 处理游戏中的输入
     */
    private void handleGameInput(String command, String[] parts) {
        switch (command) {
            case "play":
                handlePlayCommand(parts);
                break;
            case "discard":
                handleDiscardCommand(parts);
                break;
            case "tarot":
                handleTarotCommand(parts);
                break;
            case "planet":
                handlePlanetCommand(parts);
                break;
            case "spectral":
                handleSpectralCommand(parts);
                break;
            case "sort":
                handleSortCommand();
                break;
            case "skip":
                handleSkipCommand();
                break;
            case "info":
                showDetailedInfo();
                break;
            case "help":
                showHelp();
                break;
            case "quit":
                System.out.println("感谢游玩!");
                System.exit(0);
                break;
            default:
                System.out.println("未知命令。输入 'help' 查看帮助。");
        }
    }

    /**
     * 处理补充包中的输入
     */
    private void handleBoosterInput(String command, String[] parts) {
        switch (command) {
            case "select":
                handleSelectCommand(parts);
                break;
            case "skip":
                handleSkipBoosterCommand();
                break;
            case "quit":
                System.out.println("感谢游玩!");
                System.exit(0);
                break;
            default:
                System.out.println("未知命令。在补充包中可使用: select, skip, quit");
        }
    }

    /**
     * 处理商店中的输入
     */
    private void handleShopInput(String command, String[] parts) {
        switch (command) {
            case "buy":
                handleBuyCommand(parts);
                break;
            case "reroll":
                handleRerollCommand();
                break;
            case "leave":
                handleLeaveShopCommand();
                break;
            case "quit":
                System.out.println("感谢游玩!");
                System.exit(0);
                break;
            default:
                System.out.println("未知命令。在商店中可使用: buy, reroll, leave, quit");
        }
    }

    /**
     * 处理出牌命令
     */
    private void handlePlayCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要出的牌。例: play 1 2 3");
            return;
        }

        List<Card> selectedCards = new ArrayList<>();
        List<Card> hand = gameEngine.getGameState().getHand();

        try {
            for (int i = 1; i < parts.length; i++) {
                int index = Integer.parseInt(parts[i]) - 1;
                if (index >= 0 && index < hand.size()) {
                    selectedCards.add(hand.get(index));
                } else {
                    System.out.println("无效的卡牌编号: " + (index + 1));
                    return;
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的卡牌编号。");
            return;
        }

        PlayResult result = gameEngine.playHand(selectedCards);
        System.out.println();
        System.out.println("出牌结果:");
        System.out.println("  " + result.toString());

        if (result.getHandResult() != null) {
            System.out.println("  手牌类型: " + result.getHandResult().getHandType().getName());
            System.out.println("  计分卡牌: " + result.getHandResult().getScoringCards().stream()
                                                    .map(Card::toString)
                                                    .collect(Collectors.joining(", ")));
        }

        if (result.isSuccess()) {
            System.out.println("  🎉 击败盲注!");
            if (gameEngine.getGameState().isWon()) {
                System.out.println("  🏆 恭喜！你赢得了游戏!");
            }
        }
    }

    /**
     * 处理弃牌命令
     */
    private void handleDiscardCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要弃的牌。例: discard 1 2");
            return;
        }

        List<Card> selectedCards = new ArrayList<>();
        List<Card> hand = gameEngine.getGameState().getHand();

        try {
            for (int i = 1; i < parts.length; i++) {
                int index = Integer.parseInt(parts[i]) - 1;
                if (index >= 0 && index < hand.size()) {
                    selectedCards.add(hand.get(index));
                } else {
                    System.out.println("无效的卡牌编号: " + (index + 1));
                    return;
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的卡牌编号。");
            return;
        }

        DiscardResult result = gameEngine.discardCards(selectedCards);
        System.out.println();
        System.out.println("弃牌结果: " + result.getMessage());
    }

    /**
     * 处理跳过命令
     */
    private void handleSkipCommand() {
        if (gameEngine.skipBlind()) {
            System.out.println();
            System.out.println("跳过盲注成功!");
        } else {
            System.out.println();
            System.out.println("无法跳过Boss盲注!");
        }
    }

    /**
     * 处理排序命令
     */
    private void handleSortCommand() {
        gameEngine.toggleSortType();
        System.out.println();
        System.out.println("已切换到: " + gameEngine.getGameState().getSortType().getName());
        System.out.println("说明: " + gameEngine.getGameState().getSortType().getDescription());
    }

    /**
     * 处理购买命令
     */
    private void handleBuyCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要购买的商品编号。例: buy 1");
            return;
        }

        try {
            int itemIndex = Integer.parseInt(parts[1]) - 1;
            if (gameEngine.buyShopItem(itemIndex)) {
                System.out.println();
                System.out.println("购买成功!");
            } else {
                System.out.println();
                System.out.println("购买失败！可能是金钱不足或商品不存在。");
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的商品编号。");
        }
    }

    /**
     * 处理重新刷新命令
     */
    private void handleRerollCommand() {
        if (gameEngine.rerollShop()) {
            System.out.println();
            System.out.println("商店已重新刷新!");
        } else {
            System.out.println();
            System.out.println("重新刷新失败！金钱不足。");
        }
    }

    /**
     * 处理离开商店命令
     */
    private void handleLeaveShopCommand() {
        gameEngine.exitShop();
        System.out.println();
        System.out.println("已离开商店。");
    }

    /**
     * 处理选择命令
     */
    private void handleSelectCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要选择的卡牌编号。例: select 1 2");
            return;
        }

        List<Integer> selectedIndices = new ArrayList<>();

        try {
            for (int i = 1; i < parts.length; i++) {
                int index = Integer.parseInt(parts[i]) - 1;
                selectedIndices.add(index);
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的卡牌编号。");
            return;
        }

        if (gameEngine.selectBoosterPackItems(selectedIndices)) {
            System.out.println();
            System.out.println("选择成功！卡牌已应用。");
        } else {
            System.out.println();
            System.out.println("选择失败！请检查选择数量和编号。");
        }
    }

    /**
     * 处理跳过补充包命令
     */
    private void handleSkipBoosterCommand() {
        gameEngine.skipBoosterPack();
        System.out.println();
        System.out.println("已跳过补充包。");
    }

    /**
     * 处理塔罗牌命令
     */
    private void handleTarotCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要使用的塔罗牌编号。例: tarot T1 1 2");
            return;
        }

        try {
            // 解析塔罗牌编号
            String tarotStr = parts[1].toUpperCase();
            if (!tarotStr.startsWith("T")) {
                System.out.println("塔罗牌编号必须以T开头。例: T1");
                return;
            }

            int tarotIndex = Integer.parseInt(tarotStr.substring(1)) - 1;

            // 解析目标手牌编号
            List<Integer> targetCardIndices = new ArrayList<>();
            for (int i = 2; i < parts.length; i++) {
                int cardIndex = Integer.parseInt(parts[i]) - 1;
                targetCardIndices.add(cardIndex);
            }

            if (gameEngine.useTarotCard(tarotIndex, targetCardIndices)) {
                System.out.println();
                System.out.println("塔罗牌使用成功！");
            } else {
                System.out.println();
                System.out.println("塔罗牌使用失败！请检查编号。");
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的编号。");
        }
    }

    /**
     * 处理星球牌命令
     */
    private void handlePlanetCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要使用的星球牌编号。例: planet P1");
            return;
        }

        try {
            // 解析星球牌编号
            String planetStr = parts[1].toUpperCase();
            if (!planetStr.startsWith("P")) {
                System.out.println("星球牌编号必须以P开头。例: P1");
                return;
            }

            int planetIndex = Integer.parseInt(planetStr.substring(1)) - 1;

            if (gameEngine.usePlanetCard(planetIndex)) {
                System.out.println();
                System.out.println("星球牌使用成功！");
            } else {
                System.out.println();
                System.out.println("星球牌使用失败！请检查编号。");
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的编号。");
        }
    }

    /**
     * 处理幽灵牌命令
     */
    private void handleSpectralCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要使用的幽灵牌编号。例: spectral S1 1 2");
            return;
        }

        try {
            // 解析幽灵牌编号
            String spectralStr = parts[1].toUpperCase();
            if (!spectralStr.startsWith("S")) {
                System.out.println("幽灵牌编号必须以S开头。例: S1");
                return;
            }

            int spectralIndex = Integer.parseInt(spectralStr.substring(1)) - 1;

            // 解析目标手牌编号
            List<Integer> targetCardIndices = new ArrayList<>();
            for (int i = 2; i < parts.length; i++) {
                int cardIndex = Integer.parseInt(parts[i]) - 1;
                targetCardIndices.add(cardIndex);
            }

            if (gameEngine.useSpectralCard(spectralIndex, targetCardIndices)) {
                System.out.println();
                System.out.println("幽灵牌使用成功！");
            } else {
                System.out.println();
                System.out.println("幽灵牌使用失败！请检查编号。");
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的编号。");
        }
    }

    /**
     * 显示详细信息
     */
    private void showDetailedInfo() {
        System.out.println();
        System.out.println("=== 详细信息 ===");

        GameState state = gameEngine.getGameState();

        // 显示牌组信息
        System.out.println(String.format("牌组剩余: %d张", state.getDeck().size()));
        System.out.println(String.format("弃牌堆: %d张", state.getDiscardPile().size()));
        System.out.println(String.format("手牌大小: %d", state.getHandSize()));
        System.out.println(String.format("小丑槽位: %d/%d", state.getJokers().size(), state.getJokerSlots()));

        // 显示手牌类型等级
        System.out.println();
        System.out.println("手牌类型等级:");
        for (PokerHand hand : state.getUnlockedHands()) {
            System.out.println(String.format("  %s: Lv.%d (%d筹码 x %d倍数)",
                                            hand.getName(), hand.getLevel(),
                                            hand.getCurrentChips(), hand.getCurrentMult()));
        }

        System.out.println();
    }

    /**
     * 显示帮助
     */
    private void showHelp() {
        System.out.println();
        System.out.println("=== 帮助 ===");
        System.out.println("扑克手牌类型 (从低到高):");
        System.out.println("  1. 高牌 - 单张最高牌");
        System.out.println("  2. 对子 - 两张相同点数");
        System.out.println("  3. 两对 - 两个对子");
        System.out.println("  4. 三条 - 三张相同点数");
        System.out.println("  5. 顺子 - 五张连续点数");
        System.out.println("  6. 同花 - 五张相同花色");
        System.out.println("  7. 葫芦 - 三条+对子");
        System.out.println("  8. 四条 - 四张相同点数");
        System.out.println("  9. 同花顺 - 同花+顺子");
        System.out.println("  10. 皇家同花顺 - A-K-Q-J-10同花顺");
        System.out.println();
        System.out.println("秘密手牌 (需要特殊条件解锁):");
        System.out.println("  11. 五条 - 五张相同点数");
        System.out.println("  12. 同花葫芦 - 同花+葫芦");
        System.out.println("  13. 同花五条 - 同花+五条");
        System.out.println();
        System.out.println("分数计算: (基础筹码 + 卡牌筹码) × (基础倍数 + 卡牌倍数) × 倍数乘数");
        System.out.println();
    }

    /**
     * 显示游戏结束
     */
    private void showGameEnd() {
        System.out.println();
        System.out.println(repeatString("=", 60));

        if (gameEngine.getGameState().isWon()) {
            System.out.println("🏆 恭喜！你赢得了游戏！");
            System.out.println("你成功击败了所有8个Ante！");
        } else {
            System.out.println("💀 游戏结束");
            System.out.println("你在Ante " + gameEngine.getGameState().getAnte() + "失败了。");
        }

        System.out.println("最终金钱: $" + gameEngine.getGameState().getMoney());
        System.out.println(repeatString("=", 60));
        System.out.println("感谢游玩 Balatro!");
    }
}
