package com.vincenttho.game.balatro.ui;

import com.vincenttho.game.balatro.engine.GameEngine;
import com.vincenttho.game.balatro.model.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 游戏用户界面
 */
public class GameUI {
    private final GameEngine gameEngine;
    private final Scanner scanner;

    public GameUI() {
        this.gameEngine = new GameEngine();
        this.scanner = new Scanner(System.in);
    }

    /**
     * 重复字符串的辅助方法 (Java 8兼容)
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 启动游戏
     */
    public void start() {
        System.out.println(repeatString("=", 60));
        System.out.println("欢迎来到 Balatro - Java命令行版本!");
        System.out.println(repeatString("=", 60));
        System.out.println();

        showInstructions();

        while (!gameEngine.getGameState().isGameOver()) {
            showGameState();
            showHand();
            showMenu();

            String input = scanner.nextLine().trim().toLowerCase();
            handleInput(input);

            System.out.println();
        }

        showGameEnd();
    }

    /**
     * 显示游戏说明
     */
    private void showInstructions() {
        System.out.println("游戏说明:");
        System.out.println("- 这是一个基于扑克的Roguelike卡牌构建游戏");
        System.out.println("- 通过出牌获得分数来击败盲注");
        System.out.println("- 每个Ante有3个盲注：小盲注、大盲注、Boss盲注");
        System.out.println("- 击败8个Ante即可获胜");
        System.out.println("- 使用小丑牌来增强你的分数");
        System.out.println();
        System.out.println("命令:");
        System.out.println("- play [卡牌编号...] : 出牌 (例: play 1 2 3)");
        System.out.println("- discard [卡牌编号...] : 弃牌 (例: discard 1 2)");
        System.out.println("- skip : 跳过当前盲注 (不能跳过Boss盲注)");
        System.out.println("- info : 显示详细信息");
        System.out.println("- help : 显示帮助");
        System.out.println("- quit : 退出游戏");
        System.out.println();
        System.out.println("按回车键开始游戏...");
        scanner.nextLine();
    }

    /**
     * 显示游戏状态
     */
    private void showGameState() {
        GameState state = gameEngine.getGameState();
        Blind currentBlind = state.getCurrentBlind();

        System.out.println(repeatString("=", 60));
        System.out.println(String.format("Ante %d - %s", state.getAnte(), state.getCurrentBlindName()));
        System.out.println(repeatString("=", 60));

        if (currentBlind != null) {
            int requiredScore = currentBlind.getRequiredScore(state.getAnte());
            int currentScore = state.getCurrentScore();
            System.out.println(String.format("盲注: %s", currentBlind.getName()));
            System.out.println(String.format("需要分数: %,d", requiredScore));
            System.out.println(String.format("当前分数: %,d", currentScore));
            System.out.println(String.format("奖励: $%d", currentBlind.getActualReward(state.getAnte())));

            if (currentBlind instanceof BossBlind) {
                BossBlind boss = (BossBlind) currentBlind;
                System.out.println(String.format("Boss效果: %s", boss.getAbility()));
            }
        }

        System.out.println();
        System.out.println(String.format("金钱: $%d | 手牌次数: %d | 弃牌次数: %d",
                                        state.getMoney(), state.getHands(), state.getDiscards()));

        // 显示小丑牌
        if (!state.getJokers().isEmpty()) {
            System.out.println();
            System.out.println("小丑牌:");
            for (int i = 0; i < state.getJokers().size(); i++) {
                Joker joker = state.getJokers().get(i);
                System.out.println(String.format("  %d. %s - %s", i + 1, joker.toString(), joker.getDescription()));
            }
        }

        System.out.println();
    }

    /**
     * 显示手牌
     */
    private void showHand() {
        GameState state = gameEngine.getGameState();
        List<Card> hand = state.getHand();

        System.out.println(String.format("手牌 (%s):", state.getSortType().getName()));
        if (hand.isEmpty()) {
            System.out.println("  (无手牌)");
        } else {
            for (int i = 0; i < hand.size(); i++) {
                Card card = hand.get(i);
                System.out.println(String.format("  %d. %s (%d筹码)", i + 1, card.toString(), card.getBaseChips()));
            }
        }
        System.out.println();
    }

    /**
     * 显示菜单
     */
    private void showMenu() {
        System.out.println("请选择操作:");
        System.out.println("  play [卡牌编号...] - 出牌");
        System.out.println("  discard [卡牌编号...] - 弃牌");
        System.out.println("  skip - 跳过盲注");
        System.out.println("  info - 详细信息");
        System.out.println("  help - 帮助");
        System.out.println("  quit - 退出");
        System.out.print("> ");
    }

    /**
     * 处理用户输入
     */
    private void handleInput(String input) {
        String[] parts = input.split("\\s+");
        String command = parts[0];

        switch (command) {
            case "play":
                handlePlayCommand(parts);
                break;
            case "discard":
                handleDiscardCommand(parts);
                break;
            case "skip":
                handleSkipCommand();
                break;
            case "info":
                showDetailedInfo();
                break;
            case "help":
                showHelp();
                break;
            case "quit":
                System.out.println("感谢游玩!");
                System.exit(0);
                break;
            default:
                System.out.println("未知命令。输入 'help' 查看帮助。");
        }
    }

    /**
     * 处理出牌命令
     */
    private void handlePlayCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要出的牌。例: play 1 2 3");
            return;
        }

        List<Card> selectedCards = new ArrayList<>();
        List<Card> hand = gameEngine.getGameState().getHand();

        try {
            for (int i = 1; i < parts.length; i++) {
                int index = Integer.parseInt(parts[i]) - 1;
                if (index >= 0 && index < hand.size()) {
                    selectedCards.add(hand.get(index));
                } else {
                    System.out.println("无效的卡牌编号: " + (index + 1));
                    return;
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的卡牌编号。");
            return;
        }

        PlayResult result = gameEngine.playHand(selectedCards);
        System.out.println();
        System.out.println("出牌结果:");
        System.out.println("  " + result.toString());

        if (result.getHandResult() != null) {
            System.out.println("  手牌类型: " + result.getHandResult().getHandType().getName());
            System.out.println("  计分卡牌: " + result.getHandResult().getScoringCards().stream()
                                                    .map(Card::toString)
                                                    .collect(Collectors.joining(", ")));
        }

        if (result.isSuccess()) {
            System.out.println("  🎉 击败盲注!");
            if (gameEngine.getGameState().isWon()) {
                System.out.println("  🏆 恭喜！你赢得了游戏!");
            }
        }
    }

    /**
     * 处理弃牌命令
     */
    private void handleDiscardCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("请指定要弃的牌。例: discard 1 2");
            return;
        }

        List<Card> selectedCards = new ArrayList<>();
        List<Card> hand = gameEngine.getGameState().getHand();

        try {
            for (int i = 1; i < parts.length; i++) {
                int index = Integer.parseInt(parts[i]) - 1;
                if (index >= 0 && index < hand.size()) {
                    selectedCards.add(hand.get(index));
                } else {
                    System.out.println("无效的卡牌编号: " + (index + 1));
                    return;
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("请输入有效的卡牌编号。");
            return;
        }

        DiscardResult result = gameEngine.discardCards(selectedCards);
        System.out.println();
        System.out.println("弃牌结果: " + result.getMessage());
    }

    /**
     * 处理跳过命令
     */
    private void handleSkipCommand() {
        if (gameEngine.skipBlind()) {
            System.out.println();
            System.out.println("跳过盲注成功!");
        } else {
            System.out.println();
            System.out.println("无法跳过Boss盲注!");
        }
    }

    /**
     * 显示详细信息
     */
    private void showDetailedInfo() {
        System.out.println();
        System.out.println("=== 详细信息 ===");

        GameState state = gameEngine.getGameState();

        // 显示牌组信息
        System.out.println(String.format("牌组剩余: %d张", state.getDeck().size()));
        System.out.println(String.format("弃牌堆: %d张", state.getDiscardPile().size()));
        System.out.println(String.format("手牌大小: %d", state.getHandSize()));
        System.out.println(String.format("小丑槽位: %d/%d", state.getJokers().size(), state.getJokerSlots()));

        // 显示手牌类型等级
        System.out.println();
        System.out.println("手牌类型等级:");
        for (PokerHand hand : state.getUnlockedHands()) {
            System.out.println(String.format("  %s: Lv.%d (%d筹码 x %d倍数)",
                                            hand.getName(), hand.getLevel(),
                                            hand.getCurrentChips(), hand.getCurrentMult()));
        }

        System.out.println();
    }

    /**
     * 显示帮助
     */
    private void showHelp() {
        System.out.println();
        System.out.println("=== 帮助 ===");
        System.out.println("扑克手牌类型 (从低到高):");
        System.out.println("  1. 高牌 - 单张最高牌");
        System.out.println("  2. 对子 - 两张相同点数");
        System.out.println("  3. 两对 - 两个对子");
        System.out.println("  4. 三条 - 三张相同点数");
        System.out.println("  5. 顺子 - 五张连续点数");
        System.out.println("  6. 同花 - 五张相同花色");
        System.out.println("  7. 葫芦 - 三条+对子");
        System.out.println("  8. 四条 - 四张相同点数");
        System.out.println("  9. 同花顺 - 同花+顺子");
        System.out.println("  10. 皇家同花顺 - A-K-Q-J-10同花顺");
        System.out.println();
        System.out.println("秘密手牌 (需要特殊条件解锁):");
        System.out.println("  11. 五条 - 五张相同点数");
        System.out.println("  12. 同花葫芦 - 同花+葫芦");
        System.out.println("  13. 同花五条 - 同花+五条");
        System.out.println();
        System.out.println("分数计算: (基础筹码 + 卡牌筹码) × (基础倍数 + 卡牌倍数) × 倍数乘数");
        System.out.println();
    }

    /**
     * 显示游戏结束
     */
    private void showGameEnd() {
        System.out.println();
        System.out.println(repeatString("=", 60));

        if (gameEngine.getGameState().isWon()) {
            System.out.println("🏆 恭喜！你赢得了游戏！");
            System.out.println("你成功击败了所有8个Ante！");
        } else {
            System.out.println("💀 游戏结束");
            System.out.println("你在Ante " + gameEngine.getGameState().getAnte() + "失败了。");
        }

        System.out.println("最终金钱: $" + gameEngine.getGameState().getMoney());
        System.out.println(repeatString("=", 60));
        System.out.println("感谢游玩 Balatro!");
    }
}
