package com.vincenttho.game.balatro.util;

import com.vincenttho.game.balatro.model.*;

import java.util.*;

/**
 * 手牌排序工具类
 */
public class HandSorter {
    
    /**
     * 根据排序方式对手牌进行排序
     */
    public static void sortHand(List<Card> hand, SortType sortType) {
        switch (sortType) {
            case BY_RANK:
                sortByRank(hand);
                break;
            case BY_SUIT:
                sortBySuit(hand);
                break;
        }
    }
    
    /**
     * 按点数排序（从大到小）
     */
    private static void sortByRank(List<Card> hand) {
        hand.sort(new Comparator<Card>() {
            @Override
            public int compare(Card c1, Card c2) {
                // 先按点数排序（从大到小）
                int rankCompare = Integer.compare(c2.getRank().getValue(), c1.getRank().getValue());
                if (rankCompare != 0) {
                    return rankCompare;
                }
                // 点数相同时按花色排序（黑桃>红桃>方块>梅花）
                return compareSuit(c1.getSuit(), c2.getSuit());
            }
        });
    }
    
    /**
     * 按花色排序（花色分组，组内按点数从大到小）
     */
    private static void sortBySuit(List<Card> hand) {
        hand.sort(new Comparator<Card>() {
            @Override
            public int compare(Card c1, Card c2) {
                // 先按花色排序（黑桃>红桃>方块>梅花）
                int suitCompare = compareSuit(c1.getSuit(), c2.getSuit());
                if (suitCompare != 0) {
                    return suitCompare;
                }
                // 花色相同时按点数排序（从大到小）
                return Integer.compare(c2.getRank().getValue(), c1.getRank().getValue());
            }
        });
    }
    
    /**
     * 比较花色优先级（黑桃>红桃>方块>梅花）
     */
    private static int compareSuit(Suit suit1, Suit suit2) {
        int priority1 = getSuitPriority(suit1);
        int priority2 = getSuitPriority(suit2);
        return Integer.compare(priority1, priority2);
    }
    
    /**
     * 获取花色优先级
     */
    private static int getSuitPriority(Suit suit) {
        switch (suit) {
            case SPADES:   return 1; // 黑桃最高
            case HEARTS:   return 2; // 红桃
            case DIAMONDS: return 3; // 方块
            case CLUBS:    return 4; // 梅花最低
            default:       return 5;
        }
    }
    
    /**
     * 获取排序后的手牌副本（不修改原手牌）
     */
    public static List<Card> getSortedHand(List<Card> hand, SortType sortType) {
        List<Card> sortedHand = new ArrayList<>(hand);
        sortHand(sortedHand, sortType);
        return sortedHand;
    }
}
