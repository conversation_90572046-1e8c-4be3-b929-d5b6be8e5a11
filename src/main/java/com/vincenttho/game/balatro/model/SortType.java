package com.vincenttho.game.balatro.model;

/**
 * 手牌排序方式
 */
public enum SortType {
    BY_RANK("按点数排序", "点数从大到小排序"),
    BY_SUIT("按花色排序", "花色分组排序");
    
    private final String name;
    private final String description;
    
    SortType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
}
