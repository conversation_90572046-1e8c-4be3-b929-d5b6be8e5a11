package com.vincenttho.game.balatro.model;

/**
 * Boss盲注类
 */
public class Boss<PERSON><PERSON> extends Blind {
    private final String ability;
    private final BossBlindEffect effect;
    
    public BossBlind(String name, String description, String ability, int baseScore, int reward, BossBlindEffect effect) {
        super(name, description, BlindType.BOSS, baseScore, reward);
        this.ability = ability;
        this.effect = effect;
    }
    
    public String getAbility() { return ability; }
    public BossBlindEffect getEffect() { return effect; }
    
    @Override
    public void applyEffect(GameState gameState) {
        if (effect != null) {
            effect.apply(gameState);
        }
    }
    
    @Override
    public void removeEffect(GameState gameState) {
        if (effect != null) {
            effect.remove(gameState);
        }
    }
    
    @Override
    public String toString() {
        return getName() + " - " + ability;
    }
}
