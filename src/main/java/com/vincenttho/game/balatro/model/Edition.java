package com.vincenttho.game.balatro.model;

/**
 * 卡牌版本类型
 */
public enum Edition {
    BASE("基础", "标准卡牌"),
    FOIL("闪亮", "+50筹码"),
    HOLOGRAPHIC("全息", "+10倍数"),
    POLYCHROME("彩色", "X1.5倍数"),
    NEGATIVE("负片", "+1小丑槽位");
    
    private final String name;
    private final String description;
    
    Edition(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
}
