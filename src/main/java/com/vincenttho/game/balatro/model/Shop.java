package com.vincenttho.game.balatro.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 商店类
 */
public class Shop {
    private List<ShopItem> items;
    private int rerollCost;
    private boolean visited;
    
    public Shop() {
        this.items = new ArrayList<>();
        this.rerollCost = 5;
        this.visited = false;
    }
    
    public List<ShopItem> getItems() {
        return items;
    }
    
    public void setItems(List<ShopItem> items) {
        this.items = items;
    }
    
    public int getRerollCost() {
        return rerollCost;
    }
    
    public void setRerollCost(int rerollCost) {
        this.rerollCost = rerollCost;
    }
    
    public void increaseRerollCost() {
        this.rerollCost++;
    }
    
    public void resetRerollCost() {
        this.rerollCost = 5;
    }
    
    public boolean isVisited() {
        return visited;
    }
    
    public void setVisited(boolean visited) {
        this.visited = visited;
    }
    
    /**
     * 购买物品
     */
    public boolean buyItem(ShopItem item, GameState gameState) {
        if (!items.contains(item)) {
            return false;
        }
        
        if (gameState.getMoney() < item.getPrice()) {
            return false;
        }
        
        // 扣除金钱
        gameState.addMoney(-item.getPrice());
        
        // 添加物品到游戏状态
        boolean success = false;
        switch (item.getType()) {
            case JOKER:
                if (gameState.canAddJoker()) {
                    success = gameState.addJoker((Joker) item.getItem());
                }
                break;
            case TAROT:
            case PLANET:
            case SPECTRAL:
                // 消耗品直接使用或添加到库存
                success = true;
                break;
            case BOOSTER_PACK:
                // 补充包直接打开
                success = true;
                break;
            case VOUCHER:
                // 优惠券直接应用效果
                success = true;
                break;
        }
        
        if (success) {
            items.remove(item);
        } else {
            // 退还金钱
            gameState.addMoney(item.getPrice());
        }
        
        return success;
    }
    
    /**
     * 重新刷新商店
     */
    public boolean reroll(GameState gameState) {
        if (gameState.getMoney() < rerollCost) {
            return false;
        }
        
        gameState.addMoney(-rerollCost);
        increaseRerollCost();
        
        // 移除所有非补充包和非优惠券的物品
        items.removeIf(item -> item.getType() == ShopItemType.JOKER || 
                              item.getType() == ShopItemType.TAROT || 
                              item.getType() == ShopItemType.PLANET || 
                              item.getType() == ShopItemType.SPECTRAL);
        
        return true;
    }
}
