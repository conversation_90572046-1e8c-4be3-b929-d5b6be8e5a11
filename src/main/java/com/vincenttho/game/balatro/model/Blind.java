package com.vincenttho.game.balatro.model;

/**
 * 盲注类
 */
public class Blind {
    private final String name;
    private final String description;
    private final BlindType type;
    private final int baseScore;
    private final int reward;
    private boolean defeated;
    
    public Blind(String name, String description, BlindType type, int baseScore, int reward) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.baseScore = baseScore;
        this.reward = reward;
        this.defeated = false;
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
    public BlindType getType() { return type; }
    public int getBaseScore() { return baseScore; }
    public int getReward() { return reward; }
    public boolean isDefeated() { return defeated; }
    public void setDefeated(boolean defeated) { this.defeated = defeated; }
    
    /**
     * 获取当前Ante的实际分数要求
     */
    public int getRequiredScore(int ante) {
        double multiplier = Math.pow(1.6, ante - 1);
        return (int) (baseScore * multiplier);
    }
    
    /**
     * 获取当前Ante的实际奖励
     */
    public int getActualReward(int ante) {
        if (type == BlindType.BOSS) {
            return reward + ante; // Boss盲注额外奖励
        }
        return reward;
    }
    
    /**
     * 应用盲注效果
     */
    public void applyEffect(GameState gameState) {
        // 基础盲注没有特殊效果
        // Boss盲注的效果在具体的Boss类中实现
    }
    
    /**
     * 移除盲注效果
     */
    public void removeEffect(GameState gameState) {
        // 基础盲注没有特殊效果需要移除
    }
    
    @Override
    public String toString() {
        return name + " (" + type.getName() + ")";
    }
}
