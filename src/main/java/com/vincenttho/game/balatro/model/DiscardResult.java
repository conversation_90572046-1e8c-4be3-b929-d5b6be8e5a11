package com.vincenttho.game.balatro.model;

/**
 * 弃牌结果
 */
public class DiscardResult {
    private final boolean success;
    private final String message;
    
    public DiscardResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    
    @Override
    public String toString() {
        return message;
    }
}
