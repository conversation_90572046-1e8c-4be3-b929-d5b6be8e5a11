package com.vincenttho.game.balatro.model;

/**
 * 卡牌增强类型
 */
public enum Enhancement {
    NONE("无", "无增强"),
    BONUS("奖励", "+30筹码"),
    MULT("倍数", "+4倍数"),
    WILD("万能", "可以是任何花色"),
    GLASS("玻璃", "X2筹码，1/4概率摧毁"),
    STEEL("钢铁", "+50筹码，X1.5倍数"),
    STONE("石头", "+50筹码，无花色"),
    GOLD("黄金", "计分时获得$3"),
    LUCKY("幸运", "1/5概率+20倍数");
    
    private final String name;
    private final String description;
    
    Enhancement(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
}
