package com.vincenttho.game.balatro.model;

/**
 * 出牌结果
 */
public class PlayResult {
    private final boolean success;
    private final String message;
    private final HandResult handResult;
    private final int score;
    
    public PlayResult(boolean success, String message, HandResult handResult, int score) {
        this.success = success;
        this.message = message;
        this.handResult = handResult;
        this.score = score;
    }
    
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    public HandResult getHandResult() { return handResult; }
    public int getScore() { return score; }
    
    @Override
    public String toString() {
        if (handResult != null) {
            return String.format("%s - %s (得分: %d)", message, handResult.toString(), score);
        }
        return message;
    }
}
