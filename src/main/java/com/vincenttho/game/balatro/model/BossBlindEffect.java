package com.vincenttho.game.balatro.model;

/**
 * Boss盲注效果接口
 */
public interface BossBlindEffect {
    
    /**
     * 应用Boss盲注效果
     */
    void apply(GameState gameState);
    
    /**
     * 移除Boss盲注效果
     */
    void remove(GameState gameState);
    
    /**
     * 检查手牌是否触发Boss效果
     */
    default boolean triggersEffect(HandResult handResult, GameState gameState) {
        return false;
    }
    
    /**
     * 修改计分结果
     */
    default void modifyScore(ScoreContext context, HandResult handResult, GameState gameState) {}
}
