package com.vincenttho.game.balatro.model;

/**
 * 扑克牌花色枚举
 */
public enum Suit {
    HEARTS("♥", "红桃", "Hearts"),
    DIAMONDS("♦", "方块", "Diamonds"), 
    CLUBS("♣", "梅花", "Clubs"),
    SPADES("♠", "黑桃", "Spades");
    
    private final String symbol;
    private final String chineseName;
    private final String englishName;
    
    Suit(String symbol, String chineseName, String englishName) {
        this.symbol = symbol;
        this.chineseName = chineseName;
        this.englishName = englishName;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public String getChineseName() {
        return chineseName;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public boolean isRed() {
        return this == HEARTS || this == DIAMONDS;
    }
    
    public boolean isBlack() {
        return this == CLUBS || this == SPADES;
    }
    
    @Override
    public String toString() {
        return symbol;
    }
}
