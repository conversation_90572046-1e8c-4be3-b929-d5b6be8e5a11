package com.vincenttho.game.balatro.model;

/**
 * 计分上下文
 */
public class ScoreContext {
    private int chips;
    private int mult;
    private double xMult;
    private int money;
    private boolean retriggered;
    private int retriggerCount;
    
    public ScoreContext() {
        this.chips = 0;
        this.mult = 0;
        this.xMult = 1.0;
        this.money = 0;
        this.retriggered = false;
        this.retriggerCount = 0;
    }
    
    public int getChips() { return chips; }
    public void setChips(int chips) { this.chips = chips; }
    public void addChips(int chips) { this.chips += chips; }
    
    public int getMult() { return mult; }
    public void setMult(int mult) { this.mult = mult; }
    public void addMult(int mult) { this.mult += mult; }
    
    public double getXMult() { return xMult; }
    public void setXMult(double xMult) { this.xMult = xMult; }
    public void multiplyXMult(double factor) { this.xMult *= factor; }
    
    public int getMoney() { return money; }
    public void setMoney(int money) { this.money = money; }
    public void addMoney(int money) { this.money += money; }
    
    public boolean isRetriggered() { return retriggered; }
    public void setRetriggered(boolean retriggered) { this.retriggered = retriggered; }
    
    public int getRetriggerCount() { return retriggerCount; }
    public void setRetriggerCount(int retriggerCount) { this.retriggerCount = retriggerCount; }
    public void addRetrigger() { this.retriggerCount++; }
    
    public int calculateFinalScore() {
        return (int) (chips * mult * xMult);
    }
    
    @Override
    public String toString() {
        return String.format("(%d筹码 x %d倍数 x %.2f倍率 = %d分)", 
                           chips, mult, xMult, calculateFinalScore());
    }
}
