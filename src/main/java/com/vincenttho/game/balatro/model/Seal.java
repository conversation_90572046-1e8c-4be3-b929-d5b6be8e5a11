package com.vincenttho.game.balatro.model;

/**
 * 卡牌印章类型
 */
public enum Seal {
    NONE("无", "无印章"),
    GOLD("金", "计分时获得$3"),
    RED("红", "重新触发此牌"),
    BLUE("蓝", "如果保留在手中，创建星球牌"),
    PURPLE("紫", "如果弃掉，创建塔罗牌");
    
    private final String name;
    private final String description;
    
    Seal(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
}
