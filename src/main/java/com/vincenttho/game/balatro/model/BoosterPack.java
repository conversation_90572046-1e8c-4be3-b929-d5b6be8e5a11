package com.vincenttho.game.balatro.model;

import java.util.List;

/**
 * 补充包类
 */
public class BoosterPack {
    private final BoosterPackType type;
    private final BoosterPackSize size;
    private final String name;
    private final int price;
    private List<Object> contents; // 包含的卡牌
    
    public BoosterPack(BoosterPackType type, BoosterPackSize size, String name, int price) {
        this.type = type;
        this.size = size;
        this.name = name;
        this.price = price;
    }
    
    public BoosterPackType getType() { return type; }
    public BoosterPackSize getSize() { return size; }
    public String getName() { return name; }
    public int getPrice() { return price; }
    
    public List<Object> getContents() { return contents; }
    public void setContents(List<Object> contents) { this.contents = contents; }
    
    /**
     * 获取最大选择数量
     */
    public int getMaxSelections() {
        switch (size) {
            case NORMAL:
            case JUMBO:
                return 1;
            case MEGA:
                return type == BoosterPackType.STANDARD ? 2 : 2;
            default:
                return 1;
        }
    }
    
    /**
     * 获取最大选项数量
     */
    public int getMaxOptions() {
        switch (size) {
            case NORMAL:
                return type == BoosterPackType.BUFFOON || type == BoosterPackType.SPECTRAL ? 2 : 3;
            case JUMBO:
                return type == BoosterPackType.BUFFOON || type == BoosterPackType.SPECTRAL ? 4 : 5;
            case MEGA:
                return type == BoosterPackType.BUFFOON || type == BoosterPackType.SPECTRAL ? 4 : 5;
            default:
                return 3;
        }
    }
    
    @Override
    public String toString() {
        return name + " - $" + price;
    }
}
