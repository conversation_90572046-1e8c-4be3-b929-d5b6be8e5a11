package com.vincenttho.game.balatro.model;

/**
 * 补充包类型
 */
public enum BoosterPackType {
    STANDARD("标准", "包含扑克牌"),
    ARCANA("奥秘", "包含塔罗牌"),
    CELESTIAL("天体", "包含星球牌"),
    BUFFOON("小丑", "包含小丑牌"),
    SPECTRAL("幽灵", "包含幽灵牌");
    
    private final String name;
    private final String description;
    
    BoosterPackType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
}
