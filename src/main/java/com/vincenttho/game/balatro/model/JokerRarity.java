package com.vincenttho.game.balatro.model;

/**
 * 小丑牌稀有度
 */
public enum JokerRarity {
    COMMON("普通", 0.70),
    UNCOMMON("罕见", 0.25),
    RARE("稀有", 0.05),
    LEGENDARY("传奇", 0.0); // 传奇只能通过灵魂牌获得
    
    private final String name;
    private final double probability;
    
    JokerRarity(String name, double probability) {
        this.name = name;
        this.probability = probability;
    }
    
    public String getName() {
        return name;
    }
    
    public double getProbability() {
        return probability;
    }
}
