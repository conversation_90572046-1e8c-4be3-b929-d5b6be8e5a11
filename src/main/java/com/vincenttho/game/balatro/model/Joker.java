package com.vincenttho.game.balatro.model;

/**
 * 小丑牌类
 */
public class Joker {
    private final String id;
    private final String name;
    private final String description;
    private final JokerRarity rarity;
    private final int baseCost;
    private Edition edition;
    private boolean eternal;
    private boolean perishable;
    private boolean rental;
    private boolean debuffed;
    private int sellValue;
    
    // 动态属性
    private int chips;
    private int mult;
    private double xMult;
    private int money;
    private Object[] parameters; // 用于存储小丑牌的动态参数
    
    public Joker(String id, String name, String description, JokerRarity rarity, int baseCost) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.rarity = rarity;
        this.baseCost = baseCost;
        this.edition = Edition.BASE;
        this.eternal = false;
        this.perishable = false;
        this.rental = false;
        this.debuffed = false;
        this.sellValue = baseCost / 2;
        
        this.chips = 0;
        this.mult = 0;
        this.xMult = 1.0;
        this.money = 0;
        this.parameters = new Object[10]; // 预留10个参数位
    }
    
    // Getters and Setters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public JokerRarity getRarity() { return rarity; }
    public int getBaseCost() { return baseCost; }
    
    public Edition getEdition() { return edition; }
    public void setEdition(Edition edition) { this.edition = edition; }
    
    public boolean isEternal() { return eternal; }
    public void setEternal(boolean eternal) { this.eternal = eternal; }
    
    public boolean isPerishable() { return perishable; }
    public void setPerishable(boolean perishable) { this.perishable = perishable; }
    
    public boolean isRental() { return rental; }
    public void setRental(boolean rental) { this.rental = rental; }
    
    public boolean isDebuffed() { return debuffed; }
    public void setDebuffed(boolean debuffed) { this.debuffed = debuffed; }
    
    public int getSellValue() { return sellValue; }
    public void setSellValue(int sellValue) { this.sellValue = sellValue; }
    
    public int getChips() { return chips; }
    public void setChips(int chips) { this.chips = chips; }
    public void addChips(int chips) { this.chips += chips; }
    
    public int getMult() { return mult; }
    public void setMult(int mult) { this.mult = mult; }
    public void addMult(int mult) { this.mult += mult; }
    
    public double getXMult() { return xMult; }
    public void setXMult(double xMult) { this.xMult = xMult; }
    public void multiplyXMult(double factor) { this.xMult *= factor; }
    
    public int getMoney() { return money; }
    public void setMoney(int money) { this.money = money; }
    public void addMoney(int money) { this.money += money; }
    
    public Object getParameter(int index) {
        if (index >= 0 && index < parameters.length) {
            return parameters[index];
        }
        return null;
    }
    
    public void setParameter(int index, Object value) {
        if (index >= 0 && index < parameters.length) {
            parameters[index] = value;
        }
    }
    
    /**
     * 获取当前的筹码加成（包括版本效果）
     */
    public int getCurrentChips() {
        if (debuffed) return 0;
        
        int totalChips = chips;
        
        // 应用版本效果
        if (edition == Edition.FOIL) {
            totalChips += 50;
        }
        
        return totalChips;
    }
    
    /**
     * 获取当前的倍数加成（包括版本效果）
     */
    public int getCurrentMult() {
        if (debuffed) return 0;
        
        int totalMult = mult;
        
        // 应用版本效果
        if (edition == Edition.HOLOGRAPHIC) {
            totalMult += 10;
        }
        
        return totalMult;
    }
    
    /**
     * 获取当前的倍数乘数（包括版本效果）
     */
    public double getCurrentXMult() {
        if (debuffed) return 1.0;
        
        double totalXMult = xMult;
        
        // 应用版本效果
        if (edition == Edition.POLYCHROME) {
            totalXMult *= 1.5;
        }
        
        return totalXMult;
    }
    
    /**
     * 获取实际售价
     */
    public int getActualCost() {
        int cost = baseCost;
        
        // 版本影响价格
        switch (edition) {
            case FOIL:
                cost = (int) (cost * 1.5);
                break;
            case HOLOGRAPHIC:
                cost *= 2;
                break;
            case POLYCHROME:
                cost = (int) (cost * 2.5);
                break;
            case NEGATIVE:
                cost *= 3;
                break;
        }
        
        return cost;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        
        // 添加版本前缀
        switch (edition) {
            case FOIL:
                sb.append("[闪亮]");
                break;
            case HOLOGRAPHIC:
                sb.append("[全息]");
                break;
            case POLYCHROME:
                sb.append("[彩色]");
                break;
            case NEGATIVE:
                sb.append("[负片]");
                break;
        }
        
        sb.append(name);
        
        // 添加状态后缀
        if (eternal) sb.append("[永恒]");
        if (perishable) sb.append("[易腐]");
        if (rental) sb.append("[租赁]");
        if (debuffed) sb.append("[失效]");
        
        return sb.toString();
    }
}
