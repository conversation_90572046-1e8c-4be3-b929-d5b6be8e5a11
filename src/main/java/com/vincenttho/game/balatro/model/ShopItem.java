package com.vincenttho.game.balatro.model;

/**
 * 商店物品类
 */
public class ShopItem {
    private final Object item;
    private final ShopItemType type;
    private final int price;
    private final String name;
    private final String description;
    
    public ShopItem(Object item, ShopItemType type, int price, String name, String description) {
        this.item = item;
        this.type = type;
        this.price = price;
        this.name = name;
        this.description = description;
    }
    
    public Object getItem() {
        return item;
    }
    
    public ShopItemType getType() {
        return type;
    }
    
    public int getPrice() {
        return price;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return String.format("%s - $%d", name, price);
    }
}
