package com.vincenttho.game.balatro.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 游戏状态类
 */
public class GameState {
    private int ante;
    private int blindIndex; // 0=小盲注, 1=大盲注, 2=Boss盲注
    private int money;
    private int hands;
    private int discards;
    private int handSize;
    private int jokerSlots;
    private List<Card> deck;
    private List<Card> hand;
    private List<Card> discardPile;
    private List<Joker> jokers;
    private List<PokerHand> unlockedHands;
    private Blind currentBlind;
    private int currentScore; // 当前盲注的累计分数
    private SortType sortType; // 手牌排序方式
    private boolean gameOver;
    private boolean won;

    public GameState() {
        this.ante = 1;
        this.blindIndex = 0;
        this.money = 4; // 起始金钱
        this.hands = 4; // 每回合手牌数
        this.discards = 3; // 每回合弃牌数
        this.handSize = 8; // 手牌大小
        this.jokerSlots = 5; // 小丑槽位
        this.deck = new ArrayList<>();
        this.hand = new ArrayList<>();
        this.discardPile = new ArrayList<>();
        this.jokers = new ArrayList<>();
        this.unlockedHands = new ArrayList<>();
        this.currentBlind = null;
        this.currentScore = 0;
        this.sortType = SortType.BY_RANK; // 默认按点数排序
        this.gameOver = false;
        this.won = false;

        // 初始化解锁的手牌类型
        initializeUnlockedHands();
    }

    private void initializeUnlockedHands() {
        unlockedHands.add(PokerHand.HIGH_CARD);
        unlockedHands.add(PokerHand.PAIR);
        unlockedHands.add(PokerHand.TWO_PAIR);
        unlockedHands.add(PokerHand.THREE_OF_A_KIND);
        unlockedHands.add(PokerHand.STRAIGHT);
        unlockedHands.add(PokerHand.FLUSH);
        unlockedHands.add(PokerHand.FULL_HOUSE);
        unlockedHands.add(PokerHand.FOUR_OF_A_KIND);
        unlockedHands.add(PokerHand.STRAIGHT_FLUSH);
        unlockedHands.add(PokerHand.ROYAL_FLUSH);
    }

    // Getters and Setters
    public int getAnte() { return ante; }
    public void setAnte(int ante) { this.ante = ante; }
    public void nextAnte() { this.ante++; }

    public int getBlindIndex() { return blindIndex; }
    public void setBlindIndex(int blindIndex) { this.blindIndex = blindIndex; }
    public void nextBlind() { this.blindIndex++; }

    public int getMoney() { return money; }
    public void setMoney(int money) { this.money = money; }
    public void addMoney(int amount) { this.money += amount; }
    public boolean spendMoney(int amount) {
        if (money >= amount) {
            money -= amount;
            return true;
        }
        return false;
    }

    public int getHands() { return hands; }
    public void setHands(int hands) { this.hands = hands; }
    public void useHand() { this.hands--; }
    public void addHands(int amount) { this.hands += amount; }

    public int getDiscards() { return discards; }
    public void setDiscards(int discards) { this.discards = discards; }
    public void useDiscard() { this.discards--; }
    public void addDiscards(int amount) { this.discards += amount; }

    public int getHandSize() { return handSize; }
    public void setHandSize(int handSize) { this.handSize = handSize; }
    public void addHandSize(int amount) { this.handSize += amount; }

    public int getJokerSlots() { return jokerSlots; }
    public void setJokerSlots(int jokerSlots) { this.jokerSlots = jokerSlots; }
    public void addJokerSlots(int amount) { this.jokerSlots += amount; }

    public List<Card> getDeck() { return deck; }
    public void setDeck(List<Card> deck) { this.deck = deck; }

    public List<Card> getHand() { return hand; }
    public void setHand(List<Card> hand) { this.hand = hand; }

    public List<Card> getDiscardPile() { return discardPile; }
    public void setDiscardPile(List<Card> discardPile) { this.discardPile = discardPile; }

    public List<Joker> getJokers() { return jokers; }
    public void setJokers(List<Joker> jokers) { this.jokers = jokers; }

    public List<PokerHand> getUnlockedHands() { return unlockedHands; }
    public void unlockHand(PokerHand hand) {
        if (!unlockedHands.contains(hand)) {
            unlockedHands.add(hand);
        }
    }

    public Blind getCurrentBlind() { return currentBlind; }
    public void setCurrentBlind(Blind currentBlind) {
        this.currentBlind = currentBlind;
        this.currentScore = 0; // 新盲注时重置分数
    }

    public int getCurrentScore() { return currentScore; }
    public void setCurrentScore(int currentScore) { this.currentScore = currentScore; }
    public void addCurrentScore(int score) { this.currentScore += score; }

    public SortType getSortType() { return sortType; }
    public void setSortType(SortType sortType) { this.sortType = sortType; }
    public void toggleSortType() {
        this.sortType = (sortType == SortType.BY_RANK) ? SortType.BY_SUIT : SortType.BY_RANK;
    }

    public boolean isGameOver() { return gameOver; }
    public void setGameOver(boolean gameOver) { this.gameOver = gameOver; }

    public boolean isWon() { return won; }
    public void setWon(boolean won) { this.won = won; }

    /**
     * 检查是否可以添加小丑牌
     */
    public boolean canAddJoker() {
        return jokers.size() < jokerSlots;
    }

    /**
     * 添加小丑牌
     */
    public boolean addJoker(Joker joker) {
        if (canAddJoker()) {
            jokers.add(joker);
            return true;
        }
        return false;
    }

    /**
     * 移除小丑牌
     */
    public boolean removeJoker(Joker joker) {
        return jokers.remove(joker);
    }

    /**
     * 重置回合状态
     */
    public void resetRound() {
        this.hands = 4;
        this.discards = 3;
        // 应用小丑牌对手牌数和弃牌数的修改
        for (Joker joker : jokers) {
            // 这里需要根据具体小丑牌效果来调整
        }
    }

    /**
     * 获取当前盲注名称
     */
    public String getCurrentBlindName() {
        switch (blindIndex) {
            case 0: return "小盲注";
            case 1: return "大盲注";
            case 2: return "Boss盲注";
            default: return "未知盲注";
        }
    }

    @Override
    public String toString() {
        return String.format("Ante %d - %s | 金钱: $%d | 手牌: %d | 弃牌: %d",
                           ante, getCurrentBlindName(), money, hands, discards);
    }
}
