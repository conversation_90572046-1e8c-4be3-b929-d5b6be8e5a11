package com.vincenttho.game.balatro.model;

/**
 * 扑克手牌类型
 */
public enum PokerHand {
    HIGH_CARD("高牌", 5, 1, 1),
    PAIR("对子", 10, 2, 2),
    TWO_PAIR("两对", 20, 2, 3),
    THREE_OF_A_KIND("三条", 30, 3, 4),
    STRAIGHT("顺子", 30, 4, 5),
    FLUSH("同花", 35, 4, 6),
    FULL_HOUSE("葫芦", 40, 4, 7),
    FOUR_OF_A_KIND("四条", 60, 7, 8),
    STRAIGHT_FLUSH("同花顺", 100, 8, 9),
    ROYAL_FLUSH("皇家同花顺", 100, 8, 10),
    // 秘密手牌
    FIVE_OF_A_KIND("五条", 120, 12, 11),
    FLUSH_HOUSE("同花葫芦", 140, 14, 12),
    FLUSH_FIVE("同花五条", 160, 16, 13);
    
    private final String name;
    private final int baseChips;
    private final int baseMult;
    private final int priority;
    private int level;
    
    PokerHand(String name, int baseChips, int baseMult, int priority) {
        this.name = name;
        this.baseChips = baseChips;
        this.baseMult = baseMult;
        this.priority = priority;
        this.level = 1;
    }
    
    public String getName() {
        return name;
    }
    
    public int getBaseChips() {
        return baseChips;
    }
    
    public int getBaseMult() {
        return baseMult;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = Math.max(1, level);
    }
    
    public void levelUp() {
        this.level++;
    }
    
    public void levelDown() {
        this.level = Math.max(1, this.level - 1);
    }
    
    /**
     * 获取当前等级的筹码值
     */
    public int getCurrentChips() {
        return baseChips + (level - 1) * getChipsPerLevel();
    }
    
    /**
     * 获取当前等级的倍数值
     */
    public int getCurrentMult() {
        return baseMult + (level - 1) * getMultPerLevel();
    }
    
    /**
     * 获取每级增加的筹码
     */
    private int getChipsPerLevel() {
        switch (this) {
            case HIGH_CARD:
            case PAIR:
            case TWO_PAIR:
            case THREE_OF_A_KIND:
                return 10;
            case STRAIGHT:
            case FLUSH:
            case FULL_HOUSE:
                return 15;
            case FOUR_OF_A_KIND:
            case STRAIGHT_FLUSH:
            case ROYAL_FLUSH:
                return 20;
            case FIVE_OF_A_KIND:
            case FLUSH_HOUSE:
            case FLUSH_FIVE:
                return 25;
            default:
                return 10;
        }
    }
    
    /**
     * 获取每级增加的倍数
     */
    private int getMultPerLevel() {
        switch (this) {
            case HIGH_CARD:
            case PAIR:
                return 1;
            case TWO_PAIR:
            case THREE_OF_A_KIND:
            case STRAIGHT:
            case FLUSH:
                return 2;
            case FULL_HOUSE:
            case FOUR_OF_A_KIND:
                return 3;
            case STRAIGHT_FLUSH:
            case ROYAL_FLUSH:
                return 4;
            case FIVE_OF_A_KIND:
            case FLUSH_HOUSE:
            case FLUSH_FIVE:
                return 5;
            default:
                return 1;
        }
    }
    
    public boolean isSecret() {
        return this == FIVE_OF_A_KIND || this == FLUSH_HOUSE || this == FLUSH_FIVE;
    }
    
    @Override
    public String toString() {
        return name + " (Lv." + level + ")";
    }
}
