package com.vincenttho.game.balatro.model;

/**
 * 扑克牌类
 */
public class Card {
    private final Rank rank;
    private final Suit suit;
    private Enhancement enhancement;
    private Edition edition;
    private Seal seal;
    private boolean debuffed;
    
    public Card(Rank rank, Suit suit) {
        this.rank = rank;
        this.suit = suit;
        this.enhancement = Enhancement.NONE;
        this.edition = Edition.BASE;
        this.seal = Seal.NONE;
        this.debuffed = false;
    }
    
    public Rank getRank() {
        return rank;
    }
    
    public Suit getSuit() {
        return suit;
    }
    
    public Enhancement getEnhancement() {
        return enhancement;
    }
    
    public void setEnhancement(Enhancement enhancement) {
        this.enhancement = enhancement;
    }
    
    public Edition getEdition() {
        return edition;
    }
    
    public void setEdition(Edition edition) {
        this.edition = edition;
    }
    
    public Seal getSeal() {
        return seal;
    }
    
    public void setSeal(Seal seal) {
        this.seal = seal;
    }
    
    public boolean isDebuffed() {
        return debuffed;
    }
    
    public void setDebuffed(boolean debuffed) {
        this.debuffed = debuffed;
    }
    
    /**
     * 获取牌的基础筹码值
     */
    public int getBaseChips() {
        if (debuffed) return 0;
        
        int chips = rank.getChips();
        
        // 应用增强效果
        switch (enhancement) {
            case BONUS:
                chips += 30;
                break;
            case MULT:
                // Mult卡不增加筹码，只增加倍数
                break;
            case WILD:
                // Wild卡保持原筹码
                break;
            case GLASS:
                chips *= 2;
                break;
            case STEEL:
                chips += 50;
                break;
            case STONE:
                chips += 50;
                break;
            case GOLD:
                // Gold卡在计分时给钱，不影响筹码
                break;
            case LUCKY:
                // Lucky卡有概率效果，基础筹码不变
                break;
        }
        
        // 应用版本效果
        switch (edition) {
            case FOIL:
                chips += 50;
                break;
            case HOLOGRAPHIC:
                // Holographic增加Mult，不增加筹码
                break;
            case POLYCHROME:
                // Polychrome是倍数效果，不增加筹码
                break;
        }
        
        return chips;
    }
    
    /**
     * 获取牌的倍数加成
     */
    public int getMultBonus() {
        if (debuffed) return 0;
        
        int mult = 0;
        
        // 应用增强效果
        if (enhancement == Enhancement.MULT) {
            mult += 4;
        }
        
        // 应用版本效果
        if (edition == Edition.HOLOGRAPHIC) {
            mult += 10;
        }
        
        return mult;
    }
    
    /**
     * 获取牌的倍数乘数
     */
    public double getMultMultiplier() {
        if (debuffed) return 1.0;
        
        double multiplier = 1.0;
        
        // 应用版本效果
        if (edition == Edition.POLYCHROME) {
            multiplier *= 1.5;
        }
        
        return multiplier;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        
        // 添加版本前缀
        switch (edition) {
            case FOIL:
                sb.append("[闪亮]");
                break;
            case HOLOGRAPHIC:
                sb.append("[全息]");
                break;
            case POLYCHROME:
                sb.append("[彩色]");
                break;
            case NEGATIVE:
                sb.append("[负片]");
                break;
        }
        
        // 添加增强前缀
        switch (enhancement) {
            case BONUS:
                sb.append("[奖励]");
                break;
            case MULT:
                sb.append("[倍数]");
                break;
            case WILD:
                sb.append("[万能]");
                break;
            case GLASS:
                sb.append("[玻璃]");
                break;
            case STEEL:
                sb.append("[钢铁]");
                break;
            case STONE:
                sb.append("[石头]");
                break;
            case GOLD:
                sb.append("[黄金]");
                break;
            case LUCKY:
                sb.append("[幸运]");
                break;
        }
        
        sb.append(rank.getSymbol()).append("[").append(suit.getChineseName()).append("]");
        
        // 添加印章后缀
        switch (seal) {
            case GOLD:
                sb.append("($)");
                break;
            case RED:
                sb.append("(R)");
                break;
            case BLUE:
                sb.append("(B)");
                break;
            case PURPLE:
                sb.append("(P)");
                break;
        }
        
        if (debuffed) {
            sb.append("[失效]");
        }
        
        return sb.toString();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Card card = (Card) obj;
        return rank == card.rank && suit == card.suit;
    }
    
    @Override
    public int hashCode() {
        return rank.hashCode() * 31 + suit.hashCode();
    }
}
