package com.vincenttho.game.balatro.model;

/**
 * 扑克牌点数枚举
 */
public enum Rank {
    TWO(2, "2", 2),
    THREE(3, "3", 3),
    FOUR(4, "4", 4),
    <PERSON>IVE(5, "5", 5),
    <PERSON><PERSON>(6, "6", 6),
    <PERSON>VE<PERSON>(7, "7", 7),
    <PERSON><PERSON><PERSON>(8, "8", 8),
    NINE(9, "9", 9),
    TEN(10, "10", 10),
    JACK(11, "J", 10),
    QUEEN(12, "Q", 10),
    KING(13, "K", 10),
    ACE(14, "A", 11);
    
    private final int value;
    private final String symbol;
    private final int chips;
    
    Rank(int value, String symbol, int chips) {
        this.value = value;
        this.symbol = symbol;
        this.chips = chips;
    }
    
    public int getValue() {
        return value;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public int getChips() {
        return chips;
    }
    
    public boolean isFaceCard() {
        return this == JACK || this == QUEEN || this == KING;
    }
    
    public boolean isAce() {
        return this == ACE;
    }
    
    @Override
    public String toString() {
        return symbol;
    }
}
