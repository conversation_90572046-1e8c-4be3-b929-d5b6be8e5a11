package com.vincenttho.game.balatro.model;

import java.util.List;

/**
 * 手牌识别结果
 */
public class HandResult {
    private final PokerHand handType;
    private final List<Card> scoringCards;
    private final List<Card> allCards;
    private final int baseChips;
    private final int baseMult;
    
    public HandResult(PokerHand handType, List<Card> scoringCards, List<Card> allCards) {
        this.handType = handType;
        this.scoringCards = scoringCards;
        this.allCards = allCards;
        this.baseChips = handType.getCurrentChips();
        this.baseMult = handType.getCurrentMult();
    }
    
    public PokerHand getHandType() {
        return handType;
    }
    
    public List<Card> getScoringCards() {
        return scoringCards;
    }
    
    public List<Card> getAllCards() {
        return allCards;
    }
    
    public int getBaseChips() {
        return baseChips;
    }
    
    public int getBaseMult() {
        return baseMult;
    }
    
    @Override
    public String toString() {
        return handType.getName() + " (" + baseChips + "筹码 x " + baseMult + "倍数)";
    }
}
